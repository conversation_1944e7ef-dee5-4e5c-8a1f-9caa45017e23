use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// 灰度滤镜
#[tauri::command]
pub async fn filter_grayscale(
    window: tauri::Window,
    input_path: String,
    output_path: String,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!("开始应用灰度滤镜: {} -> {}", input_path, output_path);
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            "hue=s=0",
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("灰度滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 灰度滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 怀旧滤镜
#[tauri::command]
pub async fn filter_sepia(
    window: tauri::Window,
    input_path: String,
    output_path: String,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!("开始应用怀旧滤镜: {} -> {}", input_path, output_path);
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            "colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131",
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("怀旧滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 怀旧滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 浮雕滤镜
#[tauri::command]
pub async fn filter_emboss(
    window: tauri::Window,
    input_path: String,
    output_path: String,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!("开始应用浮雕滤镜: {} -> {}", input_path, output_path);
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            "convolution='-2 -1 0 -1 1 1 0 1 2'",
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("浮雕滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 浮雕滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 素描滤镜（参数：mode，可选"gray"或"color"）
#[tauri::command]
pub async fn filter_sketch(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    mode: Option<String>, // "gray" or "color"
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    let mode_str = mode.as_deref().unwrap_or("gray");
    println!(
        "开始应用素描滤镜: {} -> {}, 模式: {}",
        input_path, output_path, mode_str
    );
    let sketch_filter = match mode.as_deref() {
        Some("color") => "edgedetect=mode=colormix,format=yuv420p",
        _ => "edgedetect=mode=colormix,hue=s=0,format=yuv420p",
    };
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            sketch_filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("素描滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 素描滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 油画滤镜（可选参数：intensity，默认10）
#[tauri::command]
pub async fn filter_oilpaint(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    intensity: Option<f32>,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!("开始应用油画滤镜: {} -> {}", input_path, output_path);
    let oilpaint_filter = format!("boxblur={}:1,format=yuv420p", intensity.unwrap_or(10.0));
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &oilpaint_filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("油画滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 油画滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 马赛克滤镜（参数：block_size，默认10）
#[tauri::command]
pub async fn filter_mosaic(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    block_size: Option<u32>,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!("开始应用马赛克滤镜: {} -> {}", input_path, output_path);
    let size = block_size.unwrap_or(10);
    let mosaic_filter = format!(
        "scale=iw/{0}:ih/{0},scale=iw*{0}:ih*{0}:flags=neighbor",
        size
    );
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &mosaic_filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("马赛克滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 马赛克滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 像素化滤镜（参数：pixel_size，默认10）
#[tauri::command]
pub async fn filter_pixelate(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    pixel_size: Option<u32>,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!("开始应用像素化滤镜: {} -> {}", input_path, output_path);
    let size = pixel_size.unwrap_or(10);
    let pixelate_filter = format!(
        "scale=iw/{0}:ih/{0},scale=iw*{0}:ih*{0}:flags=neighbor",
        size
    );
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &pixelate_filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("像素化滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 像素化滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 边缘检测滤镜
#[tauri::command]
pub async fn filter_edge_detect(
    window: tauri::Window,
    input_path: String,
    output_path: String,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!("开始应用边缘检测滤镜: {} -> {}", input_path, output_path);
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            "edgedetect=low=0.1:high=0.4",
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("边缘检测滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 边缘检测滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 冷色调滤镜（参数：strength，范围0-5，越大越冷）
#[tauri::command]
pub async fn filter_cool(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    strength: Option<f32>,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!(
        "开始应用冷色调滤镜: {} -> {}, strength: {:?}",
        input_path, output_path, strength
    );
    let s = strength.unwrap_or(0.5).clamp(0.0, 5.0);
    let temp = 6500.0 + 5500.0 * s;
    let filter = format!(
        "colortemperature=temperature={},format=yuv420p",
        temp.round() as i32
    );
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args(&[
            "-i",
            &input_path,
            "-vf",
            &filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("冷色调滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 冷色调滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 暖色调滤镜（参数：strength，范围0-5，越大越暖）
#[tauri::command]
pub async fn filter_warm(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    strength: Option<f32>,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!(
        "开始应用暖色调滤镜: {} -> {}, strength: {:?}",
        input_path, output_path, strength
    );
    let s = strength.unwrap_or(0.5).clamp(0.0, 5.0);
    let temp = (6500.0 - 2500.0 * s).max(1000.0);
    let filter = format!(
        "colortemperature=temperature={},format=yuv420p",
        temp.round() as i32
    );
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args(&[
            "-i",
            &input_path,
            "-vf",
            &filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    mark_ffmpeg_running();
    window
        .emit("FILTER_PROGRESS", Payload { pct: 0.0 })
        .unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("暖色调滤镜处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("FILTER_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 暖色调滤镜完成！");
    window
        .emit("FILTER_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}
