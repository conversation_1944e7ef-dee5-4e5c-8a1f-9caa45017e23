<script lang="ts">
  import "../app.css";
  import "$lib/i18n"; // 初始化 i18n
  import { onMount } from "svelte";

  onMount(() => {
    // 禁用右键菜单
    const handleContextMenu = (event: MouseEvent) => {
      // 在生产环境中禁用右键菜单
      if (import.meta.env.PROD) {
        event.preventDefault();
      }
    };
    document.addEventListener("contextmenu", handleContextMenu);

    // 解决tauri drag_drop不生效的问题
    window.addEventListener("dragover", (e) => {
      e.preventDefault();
    });
    window.addEventListener("drop", (e) => {
      e.preventDefault();
    });

    return () => {
      document.removeEventListener("contextmenu", handleContextMenu);
    };
  });
</script>

<slot />
