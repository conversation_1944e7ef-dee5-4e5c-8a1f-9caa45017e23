<script lang="ts">
  import { cn } from "$lib/utils";

  interface Props {
    content: string;
    class?: string;
    delay?: number;
    position?: "top" | "bottom" | "left" | "right";
  }

  let {
    content,
    class: className = "",
    delay = 500,
    position = "top",
  }: Props = $props();

  let showTooltip = $state(false);
  let timeoutId: number | null = $state(null);
  let tooltipElement: HTMLElement;
  let triggerElement: HTMLElement;

  function handleMouseEnter() {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = window.setTimeout(() => {
      showTooltip = true;
    }, delay);
  }

  function handleMouseLeave() {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    showTooltip = false;
  }

  function getTooltipClasses() {
    const baseClasses =
      "absolute z-50 px-4 py-3 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none whitespace-pre-line min-w-48 max-w-sm";

    const positionClasses = {
      top: "bottom-full left-0 mb-2",
      bottom: "top-full left-0 mt-2",
      left: "right-full top-1/2 transform -translate-y-1/2 mr-2",
      right: "left-full top-1/2 transform -translate-y-1/2 ml-2",
    };

    return cn(baseClasses, positionClasses[position], className);
  }

  function getArrowClasses() {
    const arrowClasses = {
      top: "absolute top-full left-6 border-4 border-transparent border-t-gray-900",
      bottom:
        "absolute bottom-full left-6 border-4 border-transparent border-b-gray-900",
      left: "absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900",
      right:
        "absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900",
    };

    return arrowClasses[position];
  }
</script>

<div
  class="relative inline-block"
  bind:this={triggerElement}
  onmouseenter={handleMouseEnter}
  onmouseleave={handleMouseLeave}
>
  <!-- 触发区域 -->
  <slot />

  <!-- Tooltip -->
  {#if showTooltip && content}
    <div bind:this={tooltipElement} class={getTooltipClasses()}>
      {content}
      <!-- 箭头 -->
      <div class={getArrowClasses()}></div>
    </div>
  {/if}
</div>
