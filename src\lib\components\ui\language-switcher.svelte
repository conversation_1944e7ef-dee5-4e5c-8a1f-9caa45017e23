<!-- 语言切换组件 -->
<script lang="ts">
  import { Button } from "$lib/components/ui/button";
  import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "$lib/components/ui/select";
  import { locale, t, switchLocale, getSupportedLocales, getCurrentLocale } from "$lib/i18n";
  import { Globe } from "lucide-svelte";

  interface Props {
    variant?: 'button' | 'select' | 'compact';
    showIcon?: boolean;
    showLabel?: boolean;
  }

  let { variant = 'select', showIcon = true, showLabel = true }: Props = $props();

  // 获取支持的语言列表
  const supportedLocales = getSupportedLocales();
  
  // 当前语言
  let currentLocale = $state(getCurrentLocale());

  // 监听语言变化
  locale.subscribe(value => {
    if (value) currentLocale = value;
  });

  // 切换语言
  async function handleLanguageChange(newLocale: string): Promise<void> {
    try {
      await switchLocale(newLocale);
      // 可以在这里添加成功提示
      console.log(`Language switched to ${newLocale}`);
    } catch (error) {
      console.error('Failed to switch language:', error);
      // 可以在这里添加错误提示
    }
  }

  // 获取当前语言的显示名称
  function getCurrentLanguageName(): string {
    const current = supportedLocales.find(lang => lang.code === currentLocale);
    return current?.nativeName || currentLocale;
  }

  // 获取当前语言的英文名称
  function getCurrentLanguageEnglishName(): string {
    const current = supportedLocales.find(lang => lang.code === currentLocale);
    return current?.name || currentLocale;
  }
</script>

{#if variant === 'button'}
  <!-- 按钮样式 -->
  <div class="flex items-center space-x-2">
    {#if showLabel}
      <span class="text-sm text-muted-foreground">{$t('ui.language.current')}:</span>
    {/if}
    <div class="flex items-center space-x-1">
      {#each supportedLocales as lang}
        <Button
          variant={currentLocale === lang.code ? 'default' : 'outline'}
          size="sm"
          onclick={() => handleLanguageChange(lang.code)}
          class="text-xs"
        >
          {#if showIcon && lang.code === 'zh-CN'}
            🇨🇳
          {:else if showIcon && lang.code === 'en-US'}
            🇺🇸
          {/if}
          {lang.nativeName}
        </Button>
      {/each}
    </div>
  </div>

{:else if variant === 'select'}
  <!-- 下拉选择样式 -->
  <div class="flex items-center space-x-2">
    {#if showIcon}
      <Globe class="w-4 h-4 text-muted-foreground" />
    {/if}
    {#if showLabel}
      <span class="text-sm text-muted-foreground">{$t('ui.language.switch')}:</span>
    {/if}
    <Select value={currentLocale} onValueChange={handleLanguageChange}>
      <SelectTrigger class="w-auto min-w-[120px]">
        <SelectValue placeholder="选择语言" />
      </SelectTrigger>
      <SelectContent>
        {#each supportedLocales as lang}
          <SelectItem value={lang.code}>
            <div class="flex items-center space-x-2">
              {#if lang.code === 'zh-CN'}
                <span>🇨🇳</span>
              {:else if lang.code === 'en-US'}
                <span>🇺🇸</span>
              {/if}
              <span>{lang.nativeName}</span>
              <span class="text-xs text-muted-foreground">({lang.name})</span>
            </div>
          </SelectItem>
        {/each}
      </SelectContent>
    </Select>
  </div>

{:else if variant === 'compact'}
  <!-- 紧凑样式 -->
  <Button
    variant="ghost"
    size="sm"
    onclick={() => {
      // 在两种语言之间切换
      const nextLocale = currentLocale === 'zh-CN' ? 'en-US' : 'zh-CN';
      handleLanguageChange(nextLocale);
    }}
    class="flex items-center space-x-1"
    title={$t('ui.language.switch')}
  >
    {#if showIcon}
      <Globe class="w-4 h-4" />
    {/if}
    {#if showLabel}
      <span class="text-sm">{getCurrentLanguageName()}</span>
    {:else}
      <span class="text-xs">
        {currentLocale === 'zh-CN' ? '中' : 'EN'}
      </span>
    {/if}
  </Button>
{/if}

<style>
  /* 可以添加自定义样式 */
</style>
