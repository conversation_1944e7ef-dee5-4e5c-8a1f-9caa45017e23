// 媒体类型常量定义

export const MediaTypes = {
  VIDEO: 'video',
  AUDIO: 'audio',
  IMAGE: 'image',
} as const;

export type MediaType = typeof MediaTypes[keyof typeof MediaTypes];

// 支持的视频格式
export const VideoFormats = {
  MP4: 'mp4',
  AVI: 'avi',
  MOV: 'mov',
  MKV: 'mkv',
  WMV: 'wmv',
  FLV: 'flv',
  WEBM: 'webm',
} as const;

// 支持的音频格式
export const AudioFormats = {
  MP3: 'mp3',
  WAV: 'wav',
  AAC: 'aac',
  FLAC: 'flac',
  OGG: 'ogg',
  M4A: 'm4a',
} as const;

// 支持的图片格式
export const ImageFormats = {
  JPG: 'jpg',
  JPEG: 'jpeg',
  PNG: 'png',
  BMP: 'bmp',
  WEBP: 'webp',
  GIF: 'gif',
} as const;

// 分辨率预设
export const ResolutionPresets = {
  '480P': { width: 854, height: 480, label: '480p' },
  '720P': { width: 1280, height: 720, label: '720p' },
  '1080P': { width: 1920, height: 1080, label: '1080p' },
  '1440P': { width: 2560, height: 1440, label: '1440p' },
  '4K': { width: 3840, height: 2160, label: '4K' },
  'CUSTOM': { width: 0, height: 0, label: 'custom' },
} as const;

// 码率预设
export const BitratePresets = {
  LOW: '1000k',
  MEDIUM: '2000k',
  HIGH: '5000k',
  ULTRA: '10000k',
  CUSTOM: 'custom',
} as const;
