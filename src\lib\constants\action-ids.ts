// 动作ID常量定义

export const ActionIds = {
  // 裁剪类
  TRIM_START: "trim-start",
  TRIM_END: "trim-end",
  TRIM_SEGMENT: "trim-segment",
  EXCLUDE_SEGMENT: "exclude-segment",

  // 颜色调整类
  ADJUST_BRIGHTNESS: "adjust-brightness",
  ADJUST_CONTRAST: "adjust-contrast",
  ADJUST_SATURATION: "adjust-saturation",
  ADJUST_HUE: "adjust-hue",
  ADJUST_GAMMA: "adjust-gamma",
  WHITE_BALANCE: "white-balance",

  // 音频处理类
  ADJUST_VOLUME: "adjust-volume",
  EXTRACT_AUDIO: "extract-audio",
  ADD_BACKGROUND_MUSIC: "add-background-music",
  REPLACE_AUDIO: "replace-audio",
  MUTE_AUDIO: "mute-audio",

  // 变换类
  ROTATE_VIDEO: "rotate-video",
  SCALE_VIDEO: "scale-video",
  FLIP_HORIZONTAL: "flip-horizontal",
  FLIP_VERTICAL: "flip-vertical",

  // 特效类
  FADE_IN: "fade-in",
  FADE_OUT: "fade-out",
  BLUR_EFFECT: "blur-effect",
  SHARPEN_EFFECT: "sharpen-effect",
  REVERSE_VIDEO: "reverse-video",
  ADJUST_SPEED: "adjust-speed",

  // 水印类
  TEXT_WATERMARK: "text-watermark",
  IMAGE_WATERMARK: "image-watermark",
  VIDEO_WATERMARK: "video-watermark",

  // 编码类
  CONVERT_FORMAT: "convert-format",
  COMPRESS_VIDEO: "compress-video",
  ADJUST_BITRATE: "adjust-bitrate",
  ADJUST_RESOLUTION: "adjust-resolution",

  // 图片相关
  ADD_COVER_IMAGE: "add-cover-image",
  ADD_END_IMAGE: "add-end-image",
  IMAGE_TO_VIDEO: "image-to-video",
  BATCH_IMAGE_TO_VIDEO: "batch-image-to-video",
  VIDEO_TO_IMAGE: "video-to-image",
  VIDEO_TO_GIF: "video-to-gif",

  // 滤镜类
  GRAYSCALE: "grayscale",
  SEPIA: "sepia",
  EMBOSS: "emboss",
  SKETCH: "sketch",
  OIL_PAINTING: "oil-painting",
  MOSAIC: "mosaic",
  PIXELATE: "pixelate",
  EDGE_DETECTION: "edge-detection",
  VINTAGE_FILTER: "vintage-filter",
  COLD_TONE: "cold-tone",
  WARM_TONE: "warm-tone",

  // 裁剪画面
  CROP_VIDEO: "crop-video",
} as const;

export type ActionId = (typeof ActionIds)[keyof typeof ActionIds];
