<script lang="ts">
  import { cn } from "$lib/utils";

  interface Props {
    checked?: boolean;
    onCheckedChange?: (checked: boolean) => void;
    disabled?: boolean;
    class?: string;
    id?: string;
  }

  let {
    checked = $bindable(false),
    onCheckedChange,
    disabled = false,
    class: className,
    id,
    ...props
  }: Props = $props();

  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newChecked = target.checked;
    checked = newChecked;
    onCheckedChange?.(newChecked);
  }
</script>

<input
  type="checkbox"
  {id}
  {checked}
  {disabled}
  onchange={handleChange}
  class={cn(
    "peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
    className
  )}
  {...props}
/>
