use winapi::shared::windef::{HWND, RECT};
use winapi::um::winuser::{
    GetWindowRect, SetParent, SetWindowPos, ShowWindow, HWND_TOP, HWND_TOPMOST, SWP_NOZORDER,
    SWP_SHOWWINDOW, SW_SHOW,
};

/// 窗口管理器，用于处理MPV窗口的定位和嵌入
pub struct WindowManager;

impl WindowManager {
    /// 将子窗口附加到父窗口的指定区域
    pub fn attach_child_to_parent_area(
        parent_hwnd: HWND,
        child_hwnd: HWND,
        left: i32,
        top: i32,
        width: i32,
        height: i32,
    ) -> Result<(), String> {
        unsafe {
            // 设置父窗口
            let result = SetParent(child_hwnd, parent_hwnd);
            if result.is_null() {
                return Err("Failed to set parent window".to_string());
            }

            // 设置窗口位置和大小
            let result = SetWindowPos(
                child_hwnd,
                std::ptr::null_mut(),
                left,
                top,
                width,
                height,
                SWP_NOZORDER,
            );

            if result == 0 {
                return Err("Failed to set window position".to_string());
            }

            // 显示窗口
            ShowWindow(child_hwnd, SW_SHOW);
        }

        Ok(())
    }

    /// 显示窗口
    pub fn show_window(hwnd: HWND) {
        unsafe {
            ShowWindow(hwnd, SW_SHOW);
        }
    }
}

#[tauri::command]
pub async fn create_preview_window(
    app_handle: tauri::AppHandle,
    video_path: String,
    action_id: Option<String>,
) -> Result<(), String> {
    use tauri::Manager;

    // 构建URL，包含视频路径和action ID
    let mut url = format!("preview?path={}", urlencoding::encode(&video_path));
    if let Some(id) = action_id {
        url.push_str(&format!("&actionId={}", urlencoding::encode(&id)));
    }

    // 创建主预览窗口
    let preview_window = tauri::WebviewWindowBuilder::new(
        &app_handle,
        "video-preview",
        tauri::WebviewUrl::App(url.into()),
    )
    .title("视频预览")
    .inner_size(1200.0, 800.0)
    .resizable(true)
    .visible(false) // 先隐藏，等MPV窗口创建完成后再显示
    .build()
    .map_err(|e| format!("Failed to create preview window: {}", e))?;

    // 创建专门的MPV窗口
    let mpv_window = tauri::WebviewWindowBuilder::new(
        &app_handle,
        "mpv-player",
        tauri::WebviewUrl::App("about:blank".into()),
    )
    .title("MPV Player")
    .parent(&preview_window)
    .map_err(|e| format!("Failed to set parent window: {}", e))?
    .transparent(true)
    .decorations(false) // 无边框
    .resizable(false) // 禁用调整大小
    .visible(false) // 初始隐藏
    .build()
    .map_err(|e| format!("Failed to create MPV window: {}", e))?;

    // 设置父子窗口关系
    let preview_hwnd = preview_window
        .hwnd()
        .map_err(|e| format!("Failed to get preview window handle: {}", e))?;
    let mpv_hwnd = mpv_window
        .hwnd()
        .map_err(|e| format!("Failed to get MPV window handle: {}", e))?;

    WindowManager::attach_child_to_parent_area(
        preview_hwnd.0 as HWND,
        mpv_hwnd.0 as HWND,
        0,
        0,
        0, // 位置由前端MPVWindowProxy控制
        0,
    )?;

    // 初始化MPV并嵌入到MPV窗口
    crate::player::commands::init_mpv_with_window(mpv_hwnd.0 as usize)
        .await
        .map_err(|e| format!("Failed to initialize MPV: {:?}", e))?;

    // 设置窗口关闭事件处理
    let mpv_window_clone = mpv_window.clone();

    preview_window.on_window_event(move |event| {
        if let tauri::WindowEvent::CloseRequested { .. } = event {
            // 当主窗口关闭时，确保MPV窗口也关闭
            let _ = mpv_window_clone.close();

            // 清理MPV播放器
            std::thread::spawn(|| {
                if let Err(e) = crate::player::commands::cleanup_mpv_player_sync() {
                    eprintln!("清理MPV播放器失败: {:?}", e);
                }
            });
        }
    });

    // 显示窗口
    preview_window
        .show()
        .map_err(|e| format!("Failed to show preview window: {}", e))?;
    mpv_window
        .show()
        .map_err(|e| format!("Failed to show MPV window: {}", e))?;

    Ok(())
}

#[tauri::command]
pub async fn sync_mpv_window_position(
    app_handle: tauri::AppHandle,
    x: f64,
    y: f64,
    width: f64,
    height: f64,
) -> Result<(), String> {
    use tauri::Manager;

    // 获取MPV窗口
    if let Some(mpv_window) = app_handle.get_webview_window("mpv-player") {
        // 添加偏移量来调整位置，让视频更往左
        let offset_x = x - 8.0; // 向左偏移8像素
        let offset_y = y - 2.0; // 向上偏移2像素
        let adjusted_width = width; // 保持原始宽度
        let adjusted_height = height; // 保持原始高度

        // 直接使用LogicalPosition和LogicalSize，就像media-player项目一样
        mpv_window
            .set_position(tauri::Position::Logical(tauri::LogicalPosition {
                x: offset_x,
                y: offset_y,
            }))
            .map_err(|e| format!("Failed to set MPV window position: {}", e))?;

        mpv_window
            .set_size(tauri::Size::Logical(tauri::LogicalSize {
                width: adjusted_width,
                height: adjusted_height,
            }))
            .map_err(|e| format!("Failed to set MPV window size: {}", e))?;

        Ok(())
    } else {
        Err("MPV window not found".to_string())
    }
}
