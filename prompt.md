项目介绍
本项目是使用 web+tauri 实现一个视频处理工具, 基本功能是
可以应用一系列得动作(action) 到视频, 从而按顺序处理, 比如
我可以应用两个动作, 反转+切掉最后 5 分钟, 工具就按照动作得顺序依次执行,
前一次动作结果的输出就成为后一个动作的输入, 达到一种链式处理效果.

## 技术架构

- 界面使用 svelte 5 实现
- gui 实现用 tauri v2
- css 使用 tainwindcss
- 包管理 pnpm
- 组件库 shadcn-svelte
- 视频操作使用 rust 库 `ffmpeg-sidecar`
- 操作系统 windows10, 命令行: pwsh

## 前端功能

1. 添加 action
   在左边栏会列出很多 action, 每个代表一种对视频的实际操作, 比如截掉头部,
   截掉尾部, 反转视频, 调整视频颜色等等. 点击每个 action, 会在右边操作栏出现
   一个操作窗口(类似一种卡片, 但会有一些输入控件, 来让用户输入该操作需要的参数).
2. 可以添加多个 action
   可以通过左边栏添加多个 action, 每次添加一个, 就会出现在右边的最下方, action 列表
   按顺序显示, 每个 action 卡片可以有调整顺序(比如往上移动), 可以折叠, 展开, 移除等等操作

3. 右边操作栏分上下两个部分, 上面显示一个视频先择组件, 下面是要执行的 action 列表

4. 视频选择组件, 包括视频选择, 视频基本信息显示(文件名, 编码, 长度, 分辨率等等), 视频选择组件是公共的, 会一直显示在操作栏上方

5. 当添加 action 时候做判断, 不能重复添加相同的 action

6. action card 可以进行拖拽上下移动, action card 左上显示一个 handle, 可以用鼠标上下拖拽, 并显示相邻的组件移动的效果

7. 现在执行一个优化, 将左侧栏的 action 组成成一系列的类别, 比如剪切视频(点击该类别后会展开具体 action, 比如剪切头部, 剪切尾部, 剪切一段等等), 并自动生成视频软件常用的类别和具体的 action, 每个类别可以单独展开或关闭

8. 在左侧栏上方显示一个搜索框, 输入 action 名后会动态显示匹配的 action 列表

9. 实现调用 rust 获取视频信息得功能(获取时长(01:30:30)), 视频编码, 音频编码, 格式(按后缀), 大小(MB)
10. 所有操作输出视频统一命名为`原文件名_output.ext`, 比如输入为 leijun.mp4, 输出结果为 leijun_output.mp4
11. 输入文件选择组件, 支持多种输入, 包括视频, 音频, 图片, 并根据文件类型显示不同的具体
    文件信息
    视频信息: 格式, 大小, 时长, 视频编码, 音频编码, 分辨率, 帧率, 视频码率, 音频采样率, 音频声道
    音频信息: 格式, 大小, 时长, 音频编码, 音频采样率, 音频声道
    图片信息: 格式, 大小, 分辨率

## 后端功能

- 使用 ez-ffmpeg 完成 rust 对应前端的 action 相关视频功能
- 任务完成后前端提示, 最好能显示一个进度条
- 先完成一个最简单的视频截掉头部的功能, 使前后端能够连通
- 使用前端配合 tauri 调用后端功能

## 动作输入输出细节

- 以下动作支持音频, 视频输入和音频视频输出, 输出由输入类型决定
  - 截掉头部
  - 截掉尾部
  - 截取片段
  - 调整音量
  - 静音
- 以下支持图片输入, 输出视频
  - 图片转视频
- 其他默认都是输入视频, 输出视频

## 开发细节

- 前端有文件选择的功能, 默认打开当前输入视频的目录
- 所有错误不弹窗显示, 统一默认显示在视频选择下方, 处理列表上方
- 通用命名逻辑优化：
  对于最后一个 action，根据其 outputTypes 来确定最终文件扩展名
  视频输出：.mp4
  音频输出：.mp3（或用户选择的格式）
  图片输出：.png
  特殊 action 处理：
  图片转视频：移除了其特殊的命名逻辑，现在使用通用逻辑
  提取音频：如果是最后一个 action，使用通用逻辑；否则使用临时文件命名

我现在已经完成了基本页面布局设计, 左右栏的设计, 请在此基础上完成修改
并考虑我用的技术架构及版本

## 优化以及问题修复

- [x] 视频选择组件支持拖拽文件
- [x] 完成视频截掉尾部功能
- [x] 完成视频截取片段功能,用户选择开始时间, 时间用时分秒格式(00:10:30)以及结束时间(00:20:00)
- [x] 实现颜色调整相关功能
  - [x] 调整亮度 (-100 到 100)
  - [x] 调整对比度 (0.1 到 3.0)
  - [x] 调整饱和度 (0.0 到 3.0)
  - [x] 调整色相 (-180 到 180)
  - [x] 调整伽马值 (0.1 到 3.0)
  - [x] 白平衡 (色温 2000 到 12000)
- [x] 优化进度处理, 进度分两块: 1. 为当前动作进度 2. 显示总体进度, 比如一共 6 个动作连续处理, 显示一个 `动作(3/6)`, 当前是第三个动作
- [x] 多个动作连续处理问题修复, 当有多个动作在列表需要连续处理时, 这时每个动作的输出一个临时文件, 作为第二个动作的输入, 只有最后一个动作输出最终的`leijun_output.mp4`
- [x] bug: 当测试截取头部时, 时间不准确, 比如我输入 30 秒, 但后面检查输出的视频, 发现只截取了头部 25 秒
- [x] 解决编译后弹出命令窗口问题, 并测试了编译`pnpm tauri build` ,成功执行
- [x] 处理列表增加一个`清空动作` 的按钮, 放在开始处理左边, 点击后让用户确认, 确认后移除所有动作, 方便用户执行其他任务
- [x] 实现音频 action 分类中的 `调整音量` 和 `静音` 功能
- [x] 实现音频 action 分类中的 `提取音频` 功能, 提供一个下拉框, 有 5 种格式选择(wav, mp3, aac, flac, m4a), 输出文件名用`原视频文件名_output.mp3`, 后缀更改相应音频后缀
- [x] 音频 action 分类添加一个 `添加背景音乐` 的动作, 用户选择一个音频文件(mp3, wav, aac, flac, m4a) 格式, 并提供一个 checkbox(循环播放), 用户可以控制是否将背景音乐只播放一次或循环播放整个视频长度
- [x] 小优化, 将进度窗口总体进度放在动作进度下方, 方便观察
- [x] 完成音频分类中的 `替换音频`功能, 用户选择一个音频文件(mp3, wav, aac, flac, m4a) 格式,并提供一个 checkbox(循环播放), 用户可以控制是否将新的音频只播放一次或循环播放整个视频长度, 若音频比视频长, 则自动截断, 以视频长度为准
- [x] 完成剪切分类里的`裁剪画面`功能
- [x] 布局优化, 当前布局是当页面内容多时, 右边滚动条时针对整个页面的, 我需要将主体页面固定, 左右可以独立滚动, 左边动作列表长时也可以滚动(目前就是这样), 但滚动都是局部的, 不是将整个页面滚动
- [x] 实现`变换`动作分类下的所有动作(旋转, 水平翻转, 垂直翻转, 缩放)
- [x] 优化: 现在进度窗口在进行中, 无法点击取消, 加上点击后取消任务功能
- [x] 添加一个`图片处理`动作类别, 内部包括一些具体动作
  - [x] 添加封面图片- 用户可以选择一个图片插入到视频最前, 并可以指定图片时长(xx 秒)
  - [x] 添加封底图片- 用户可以选择一个图片插入到视频最后, 并可以指定图片时长(xx 秒)
  - [x] 图片转视频,用户可以选择一个图片,并可以指定图片时长(xx 秒), 并指定分辨率(提供一个下拉框 480p, 720p, 1080p, 自定义, 当选择自定义时, 出现两个输入框对应长度和宽度让用户输入, 并最终做好提交前验证不为空等)
- 优化: 每个 action 都有对应的输入和输出, 比如裁剪的输入可以是(音频, 视频), 输出也对应为(音频, 视频), 而颜色调整中的 action 输入和输出都必须为视频, 图片转视频输入为图片, 输出为视频, 所以最上面的文件选择不一定为视频, 可能为视频,音频, 图片之一, 所以 action 加上输入和输出的配置, 如果是处理列表第一个 action, 则输入为上方的文件选择输入, 后面的 action 依次为前一个 action 的输出, 这样处理开始前可以顺序判断动作链的每一个 action 是否有了正确的输入和输出, 所有判断通过才开始链式操作
- [x] 重构前端默认值设置, 将所有默认值单独放在一个函数
- [x] 重构后台代码, 现在后台 rust 代码都写在一个文件里, 按前台的动作类别, 每个类别的所有后台处理函数放在单独的模块里, lib.rs 作统一调用处理, 以及公共变量函数定义, 方便后续维护修改, 重构将逐步执行, 一步步将每个 action 类别的相关功能整理到独立模块
  - 重构只做代码分离的工作, 不做任何函数修改, 所有函数均原样复制
  - [x] 将裁剪相关 action 功能分离到独立模块 trim
  - [x] 将 lib.rs 里的公共函数分离到 common 模块
  - [x] 将颜色调整相关 action 功能分离到独立模块 color
  - [x] 将变换相关 action 功能分离到独立模块 transform
  - [x] 将音频相关 action 功能分离到独立模块 audio
  - [x] 修复音频替换的原音频依然存在的 bug
  - [x] 将图片处理相关 action 功能分离到独立模块 image
- [x] 实现`特效`动作分类下的所有动作, 包括前后台, rust 模块名 effect
  - 反转视频
  - 调整播放速度
  - 淡入效果
  - 淡出效果
  - 模糊效果
  - 锐化效果
- [x] 实现`滤镜`动作分类下的所有动作, 包括前后台, rust 模块名 filter
- [x] 添加一个`水印`动作分类, 含以下动作
  - [x] 文字水印: 输入文字做成水印, 需要有位置参数(下拉框提供左上, 右上, 左下, 右下, 自定义,当选择自定义时用户可以
        自行输入参数), 字体大小, 字体颜色
  - [x] 图片水印: 选择一个图片做成水印, 需要有宽度参数(自动调整高度匹配), 需要有位置参数(下拉框提供左上, 右上, 左下, 右下, 自定义,当选择自定义时用户可以
        自行输入参数) , 支持 jpg, jpeg, png, bmp, gif, webp 格式
  - [x] 视频水印: 选择一个视频做成水印, 需要有宽度参数(自动调整高度匹配), 需要有位置参数(下拉框提供左上, 右上, 左下, 右下, 自定义,当选择自定义时用户可以
        自行输入参数) , 支持 mp4, mov, avi, mkv, wmv, flv, webm 格式
- [x] 实现`编码`动作分类, 包括前后台, rust 模块名 encode
  - [x] 转换格式
  - [x] 压缩视频(1-10, 1 为压缩最大, 文件最小, 10 为不压缩, 默认为 5)
  - [x] 调整码率, 提供一个下拉框, 常用的码率选择, 以及自定义, 选择自定义出现文本框用户输入任意值
  - [x] 调整分辨率, 提供一个下拉框, 常用的分辨率选择, 以及自定义(参考图片转视频动作配置)
- [x] `图片处理` 动作分类添加以下功能, 包括前后台, rust 模块名 videotoimage
  - [x] 实现视频生图功能, 用户选择需要生成图片的数量, 格式(jpg, png, webp), 以及方式(随机还是均匀截取)
  - [x] 视频转动图功能, 支持gif或webp, 用户选择需要的帧数, 帧率
  - [x] `批量图片转视频`动作, 用户选择一个图片文件夹, 程序自动将文件夹内所有图片(xxxx_001.jpg, xxxx_002.jpg)转为视频, 提供格式, 可以选(总时长或每张图片显示时间, 二选一, 和一个数字输入框), 视频长度, 宽度等参数
- [x] 视频选择组件需要支持选择文件夹(以前只能选择图片, 视频, 音频), 当选择文件夹时显示文件夹文件数量, 以及文件列表前2个文件名
- [x] `批量图片转视频`动作, 添加一个页面切换效果选项, 可以选择翻页效果,`淡入淡出, 左翻页, 右翻页等`
- [x] 添加视频播放组件, 并用于视频剪辑动作需要的获取片段开始, 结束时间
- [x] `截取片段` 改为`截取多片段后合并` , 支持在预览窗口添加多个片段后合并导出视频, 并更新action 的ui,支持排序, 滚动, 删除全部以及重复片段检测等
- [x] 预览窗口支持排序, 删除全部等操作, 方便使用
- [x] 裁剪分类下添加一个`剔除多片段后合并` 动作, 该动作类似`截取多片段后合并`, 操作方式一致, 唯一区别就是后端将指定片段之外的所有视频片段合并导出, 用户可根据情况选择最合适的动作使用
- [x] 预览窗口增加进度条和音量控制
- [x] 预览窗口增加键盘快捷键操作, 方便用户快速添加片段
- [x] 增加`动作链`管理功能, 包括列表, 删除, 导入, 导出等
- [x] 优化主窗口布局设计, 最大化用户可用空间, 增加F1F2F3快捷键控制

### 已实现滤镜动作一览

| 动作名称 | 参数       | 功能说明                      |
| -------- | ---------- | ----------------------------- |
| 灰度     | 无         | 视频转为黑白灰度              |
| 怀旧     | 无         | 棕褐色怀旧风格                |
| 浮雕     | 无         | 浮雕立体效果                  |
| 素描     | mode       | 素描风格，mode: gray/彩色     |
| 油画     | intensity  | 油画/柔化风格，intensity 可调 |
| 马赛克   | block_size | 马赛克像素块大小可调          |
| 像素化   | pixel_size | 像素化像素块大小可调          |
| 边缘检测 | 无         | 边缘线条高亮                  |
| 冷色调   | strength   | 色温提升，画面偏冷，强度可调  |
| 暖色调   | strength   | 色温降低，画面偏暖，强度可调  |
| 复古滤镜 | 无         | 复用怀旧滤镜                  |
| 复古效果 | 无         | 复用浮雕滤镜                  |

- 所有滤镜动作均支持进度推送、参数校验、错误处理、链式处理。
- 前端 action-list、action-card、主流程均已联通后端命令。
- 滤镜相关 Rust 代码集中于`src-tauri/src/filter/mod.rs`，便于维护。

### 已实现水印动作一览

| 动作名称 | 参数                                                      | 功能说明                                           |
| -------- | --------------------------------------------------------- | -------------------------------------------------- |
| 文字水印 | text, position, font_size, font_color, custom_x, custom_y | 在视频上添加文字水印，支持位置、字体大小、颜色设置 |
| 图片水印 | watermarkFile, position, width, custom_x, custom_y        | 在视频上添加图片水印，支持位置、大小设置           |
| 视频水印 | watermarkFile, position, width, custom_x, custom_y        | 在视频上添加视频水印，支持位置、大小设置           |

- 所有水印动作均支持进度推送、参数校验、错误处理、链式处理。
- 前端 action-list、action-card、主流程均已联通后端命令。
- 水印相关 Rust 代码集中于`src-tauri/src/watermark/mod.rs`，便于维护。
- 位置参数支持：左上、右上、左下、右下、自定义坐标。
- 文字水印支持自定义字体大小和颜色。
- 图片和视频水印支持自定义宽度，高度自动按比例调整。

### 已实现编码动作一览

| 动作名称   | 参数                                         | 功能说明                                               |
| ---------- | -------------------------------------------- | ------------------------------------------------------ |
| 转换格式   | format                                       | 转换视频格式，支持 mp4、avi、mov、mkv、webm            |
| 压缩视频   | compression_level                            | 压缩视频文件大小，级别 1-10，1 为最大压缩，10 为不压缩 |
| 调整码率   | bitrate_type, custom_bitrate                 | 调整视频码率，支持常用码率选择和自定义输入             |
| 调整分辨率 | resolution_type, custom_width, custom_height | 调整视频分辨率，支持常用分辨率选择和自定义输入         |

- 所有编码动作均支持进度推送、参数校验、错误处理、链式处理。
- 前端 action-list、action-card、主流程均已联通后端命令。
- 编码相关 Rust 代码集中于`src-tauri/src/encode/mod.rs`，便于维护。
- 转换格式支持多种常用视频格式。
- 压缩视频使用 CRF 值控制质量，级别 1-10 对应 CRF 8-28。
- 调整码率支持常用码率选择（500k-8000k）和自定义输入。
- 调整分辨率支持常用分辨率选择（480p-4K）和自定义输入。

## 参考资料链接

ffmpeg-sidecar git 仓库 - https://github.com/nathanbabcock/ffmpeg-sidecar
ffmpeg-sidecar rust 文档 - https://docs.rs/ffmpeg-sidecar/latest/ffmpeg_sidecar/index.html
