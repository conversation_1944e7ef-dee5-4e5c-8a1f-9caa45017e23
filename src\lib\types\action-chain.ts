// 动作链相关类型定义

export interface ActionChain {
  name: string;
  description: string;
  version: string;
  actions: ActionData[];
}

export interface ActionData {
  id: string;
  name: string;
  parameters: Record<string, any>;
  order: number;
}

export interface ActionChainInfo {
  name: string;
  description: string;
  actionCount: number;
  createdAt: string;
  lastUsedAt?: string;
  useCount: number;
  actionNames: string[]; // 动作名称列表，用于预览
}

// 动作链状态
export type ActionChainState =
  | "EMPTY" // 空状态，无任何动作
  | "NEW" // 新建状态，有动作但未保存
  | "LOADED" // 已加载动作链，无修改
  | "MODIFIED"; // 已加载动作链，有修改

export interface ActionChainStatus {
  state: ActionChainState;
  name?: string;
  hasUnsavedChanges: boolean;
}
