use libloading::{Library, Symbol};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::ffi::{c_char, CStr, CString};
use std::os::raw::{c_double, c_int, c_void};
use std::sync::Arc;
use std::sync::Mutex;
use std::thread;
use thiserror::Error;
use winapi::shared::windef::HWND;

// Thread-safe wrapper for the raw pointer
struct MpvHandle(*mut c_void);
unsafe impl Send for MpvHandle {}
unsafe impl Sync for MpvHandle {}

#[derive(Error, Debug)]
pub enum MpvError {
    #[error("Library error: {0}")]
    LibraryError(#[from] libloading::Error),

    #[error("Failed to set option: {name} = {value}")]
    SetOptionError { name: String, value: String },

    #[error("Failed to initialize MPV")]
    InitializationError,

    #[error("Failed to execute command: {0}")]
    CommandError(String),

    #[error("String conversion error: {0}")]
    StringConversionError(#[from] std::ffi::NulError),

    #[error("Failed to get property: {0}")]
    GetPropertyError(String),

    #[error("Failed to set property: {0}")]
    SetPropertyError(String),

    #[error("Failed to process events")]
    EventProcessingError,
}

// Custom serializer for MpvError
impl serde::Serialize for MpvError {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::ser::Serializer,
    {
        serializer.serialize_str(self.to_string().as_ref())
    }
}

/// Mpv formats enum copied straight from mpv's client.h file.
#[repr(C)]
enum MpvFormat {
    None = 0,
    String = 1,
    OsdString = 2,
    Flag = 3,
    Int64 = 4,
    Double = 5,
    Node = 6,
    NodeArray = 7,
    NodeMap = 8,
    ByteArray = 9,
}

/// MPV event IDs from client.h
#[derive(Debug, Clone, Serialize, Deserialize)]
#[repr(C)]
pub enum MpvEventId {
    None = 0,
    Shutdown = 1,
    LogMessage = 2,
    GetPropertyReply = 3,
    SetPropertyReply = 4,
    CommandReply = 5,
    StartFile = 6,
    EndFile = 7,
    FileLoaded = 8,
    ClientMessage = 16,
    VideoReconfig = 17,
    AudioReconfig = 18,
    Seek = 20,
    PlaybackRestart = 21,
    PropertyChange = 22,
    QueueOverflow = 24,
    Hook = 25,
}

#[repr(C)]
pub struct MpvEvent {
    event_id: c_int,
    error: c_int,
    reply_userdata: u64,
    data: *mut c_void,
}

pub type EventCallback = Box<dyn Fn(&MpvEvent) + Send + 'static>;

struct Mpv {
    handle: MpvHandle,
    library: Arc<Library>,
    event_callbacks: Mutex<HashMap<c_int, Vec<EventCallback>>>,
    window_handle: Mutex<Option<usize>>, // 存储HWND作为usize以确保线程安全
}

impl Mpv {
    const EVENT_TIMEOUT: f64 = 1.0; // timeout for `mpv_wait_event()`

    fn new(lib_path: &str) -> Result<Self, MpvError> {
        let library = Arc::new(unsafe { Library::new(lib_path)? });
        let create_fn: Symbol<unsafe extern "C" fn() -> *mut c_void> =
            unsafe { library.get(b"mpv_create")? };
        let handle = unsafe { create_fn() };

        Ok(Self {
            handle: MpvHandle(handle),
            library,
            event_callbacks: Mutex::new(HashMap::new()),
            window_handle: Mutex::new(None),
        })
    }

    fn destroy(&self) -> Result<(), MpvError> {
        let destroy_fn: Symbol<unsafe extern "C" fn(*mut c_void)> =
            unsafe { self.library.get(b"mpv_destroy")? };

        unsafe { destroy_fn(self.handle.0) };
        Ok(())
    }

    fn set_option(&self, name: &str, value: &str) -> Result<(), MpvError> {
        let set_option_fn: Symbol<
            unsafe extern "C" fn(*mut c_void, *const c_char, *const c_char) -> i32,
        > = unsafe { self.library.get(b"mpv_set_option_string")? };

        let name_cstring = CString::new(name)?;
        let value_cstring = CString::new(value)?;

        let result =
            unsafe { set_option_fn(self.handle.0, name_cstring.as_ptr(), value_cstring.as_ptr()) };

        if result == 0 {
            Ok(())
        } else {
            Err(MpvError::SetOptionError {
                name: name.to_string(),
                value: value.to_string(),
            })
        }
    }

    fn initialize(&self) -> Result<(), MpvError> {
        let init_fn: Symbol<unsafe extern "C" fn(*mut c_void) -> i32> =
            unsafe { self.library.get(b"mpv_initialize")? };

        let result = unsafe { init_fn(self.handle.0) };

        if result == 0 {
            Ok(())
        } else {
            Err(MpvError::InitializationError)
        }
    }

    fn command_string(&self, command: &str) -> Result<(), MpvError> {
        let command_fn: Symbol<unsafe extern "C" fn(*mut c_void, *const c_char) -> i32> =
            unsafe { self.library.get(b"mpv_command_string")? };

        let command_cstring = CString::new(command)?;

        let result = unsafe { command_fn(self.handle.0, command_cstring.as_ptr()) };

        if result == 0 {
            Ok(())
        } else {
            Err(MpvError::CommandError(command.to_string()))
        }
    }

    fn get_property_string(&self, name: &str) -> Result<String, MpvError> {
        let get_property_string_fn: Symbol<
            unsafe extern "C" fn(*mut c_void, *const c_char) -> *mut c_char,
        > = unsafe { self.library.get(b"mpv_get_property_string")? };

        let name_cstring = CString::new(name)?;

        let result = unsafe { get_property_string_fn(self.handle.0, name_cstring.as_ptr()) };

        if result.is_null() {
            return Err(MpvError::GetPropertyError(name.to_string()));
        }

        let c_str = unsafe { CStr::from_ptr(result) };
        let string = c_str.to_str().unwrap_or("").to_string();

        self.free(result as *mut c_void)?; // free string

        Ok(string)
    }

    fn get_property_double(&self, name: &str) -> Result<f64, MpvError> {
        let get_property_fn: Symbol<
            unsafe extern "C" fn(*mut c_void, *const c_char, c_int, *mut c_void) -> i32,
        > = unsafe { self.library.get(b"mpv_get_property")? };

        let name_cstring = CString::new(name)?;
        let mut value: c_double = 0.0;

        let result = unsafe {
            get_property_fn(
                self.handle.0,
                name_cstring.as_ptr(),
                MpvFormat::Double as c_int,
                &mut value as *mut c_double as *mut c_void,
            )
        };

        if result == 0 {
            Ok(value)
        } else {
            Err(MpvError::GetPropertyError(name.to_string()))
        }
    }

    fn free(&self, ptr: *mut c_void) -> Result<(), MpvError> {
        let free_fn: Symbol<unsafe extern "C" fn(*mut c_void)> =
            unsafe { self.library.get(b"mpv_free")? };

        unsafe { free_fn(ptr) };
        Ok(())
    }
}

pub struct MpvPlayer {
    mpv: Arc<Mpv>,
}

impl MpvPlayer {
    pub fn new(lib_path: &str) -> Result<Arc<Self>, MpvError> {
        let mpv = Arc::new(Mpv::new(lib_path)?);
        let player = Arc::new(Self { mpv });
        Ok(player)
    }

    fn escape_path(path: &str) -> String {
        path.replace('\\', "\\\\")
            .replace('\'', "\\\'")
            .replace('\"', "\\\"")
    }

    pub fn destroy(&self) -> Result<(), MpvError> {
        self.mpv.destroy()
    }

    pub fn attach_to_window(&self, wid: usize) -> Result<(), MpvError> {
        // 保存窗口句柄
        self.set_window_handle(wid);
        self.mpv.set_option("wid", &wid.to_string())
    }

    pub fn initialize(&self) -> Result<(), MpvError> {
        // 设置一些选项来确保窗口能够正确关闭
        // 注意：idle=no 可能会导致MPV在没有文件时立即退出，所以我们改为yes
        self.mpv.set_option("keep-open", "yes")?; // 播放结束后保持最后画面
        self.mpv.set_option("force-window", "yes")?;
        self.mpv.set_option("idle", "yes")?;

        // 视频适配选项 - 保持比例但尽量填充
        self.mpv.set_option("keepaspect", "yes")?; // 保持宽高比
        self.mpv.set_option("keepaspect-window", "no")?; // 不调整窗口大小
        self.mpv.set_option("video-aspect-override", "no")?; // 不强制宽高比
        self.mpv.set_option("video-unscaled", "no")?; // 允许缩放
        self.mpv.set_option("panscan", "0.0")?; // 不使用平移扫描

        // 窗口相关选项
        self.mpv.set_option("border", "no")?; // 无边框
        self.mpv.set_option("osd-level", "0")?; // 关闭OSD显示
        self.mpv.set_option("cursor-autohide", "no")?; // 不自动隐藏鼠标
        self.mpv.set_option("input-cursor", "no")?; // 禁用鼠标输入
        self.mpv.set_option("input-vo-keyboard", "no")?; // 禁用键盘输入

        self.mpv.initialize()
    }

    pub fn load_file(&self, path: &str) -> Result<(), MpvError> {
        let escaped_path = Self::escape_path(path);
        let command = format!("loadfile \"{}\"", escaped_path);
        self.mpv.command_string(&command)
    }

    pub fn play(&self) -> Result<(), MpvError> {
        self.mpv.command_string("set pause no")
    }

    pub fn pause(&self) -> Result<(), MpvError> {
        self.mpv.command_string("set pause yes")
    }

    pub fn seek(&self, position: f64) -> Result<(), MpvError> {
        // 获取视频总时长进行边界检查
        let duration = self.get_duration().unwrap_or(0.0);

        // 限制在有效范围内
        let clamped_pos = if position < 0.0 {
            0.0
        } else if position >= duration && duration > 0.0 {
            duration - 0.1 // 稍微退后一点，避免到达最末尾
        } else {
            position
        };

        self.mpv
            .command_string(&format!("seek {} absolute", clamped_pos))
    }

    pub fn seek_relative(&self, offset: f64) -> Result<(), MpvError> {
        // 获取当前位置和视频总时长
        let current_pos = self.get_position().unwrap_or(0.0);
        let duration = self.get_duration().unwrap_or(0.0);

        // 计算目标位置
        let target_pos = current_pos + offset;

        // 限制在有效范围内
        let clamped_pos = if target_pos < 0.0 {
            0.0
        } else if target_pos >= duration && duration > 0.0 {
            duration - 0.1 // 稍微退后一点，避免到达最末尾
        } else {
            target_pos
        };

        // 使用绝对定位而不是相对定位，确保精确控制
        self.seek(clamped_pos)
    }

    pub fn stop(&self) -> Result<(), MpvError> {
        self.mpv.command_string("stop")
    }

    pub fn quit(&self) -> Result<(), MpvError> {
        self.mpv.command_string("quit")
    }

    pub fn get_position(&self) -> Result<f64, MpvError> {
        self.mpv.get_property_double("time-pos")
    }

    pub fn get_duration(&self) -> Result<f64, MpvError> {
        self.mpv.get_property_double("duration")
    }

    pub fn get_volume(&self) -> Result<f64, MpvError> {
        self.mpv.get_property_double("volume")
    }

    pub fn set_volume(&self, volume: f64) -> Result<(), MpvError> {
        self.mpv.command_string(&format!("set volume {}", volume))
    }

    pub fn get_mute(&self) -> Result<bool, MpvError> {
        let mute_str = self.mpv.get_property_string("mute")?;
        Ok(mute_str == "yes")
    }

    pub fn set_mute(&self, muted: bool) -> Result<(), MpvError> {
        let mute_value = if muted { "yes" } else { "no" };
        self.mpv.command_string(&format!("set mute {}", mute_value))
    }

    pub fn is_paused(&self) -> Result<bool, MpvError> {
        let paused_str = self.mpv.get_property_string("pause")?;
        Ok(paused_str == "yes")
    }

    pub fn set_speed(&self, speed: f64) -> Result<(), MpvError> {
        self.mpv.command_string(&format!("set speed {}", speed))
    }

    pub fn get_speed(&self) -> Result<f64, MpvError> {
        self.mpv.get_property_double("speed")
    }

    pub fn frame_step(&self) -> Result<(), MpvError> {
        self.mpv.command_string("frame-step")
    }

    pub fn frame_back_step(&self) -> Result<(), MpvError> {
        self.mpv.command_string("frame-back-step")
    }

    pub fn get_filename(&self) -> Result<String, MpvError> {
        self.mpv.get_property_string("filename")
    }

    pub fn get_path(&self) -> Result<String, MpvError> {
        self.mpv.get_property_string("path")
    }

    pub fn set_window_handle(&self, hwnd: usize) {
        let mut handle = self.mpv.window_handle.lock().unwrap();
        *handle = Some(hwnd);
    }

    pub fn get_window_handle(&self) -> Option<usize> {
        let handle = self.mpv.window_handle.lock().unwrap();
        *handle
    }
}
