# 变更日志

## [2024-01-XX] - 移除 ez-ffmpeg 依赖，改用 ffprobe

### 修改内容

#### 后端 (Rust)
- **移除依赖**: 从 `Cargo.toml` 中移除了 `ez-ffmpeg` 依赖
- **新增依赖**: 添加了 `tokio = { version = "1", features = ["process"] }` 用于异步进程管理
- **重构 VideoInfo 结构体**: 
  - 更新了数据结构以匹配新的 ffprobe 输出格式
  - 字段类型从 `Option<T>` 改为直接类型，简化了数据结构
  - 新增了 `filename` 字段用于显示文件名
- **重写视频信息获取逻辑**:
  - 使用 `tokio::process::Command` 调用 `ffprobe` 命令
  - 解析 JSON 格式的输出获取视频信息
  - 添加了 `get_video_info_internal` 内部函数供其他命令使用
- **更新视频处理命令**:
  - 修改了 `trim_video_start`, `trim_video_end`, `trim_video_segment` 命令
  - 使用新的视频信息获取方式计算进度
  - 简化了帧率计算逻辑（使用固定的 30fps 进行进度估算）

#### 前端 (Svelte)
- **更新 VideoInfo 接口**: 匹配新的 Rust 后端数据结构
- **添加时长格式化函数**: `formatDuration` 函数将秒数转换为 HH:MM:SS 格式
- **简化数据处理**: 移除了复杂的数据转换逻辑，直接使用后端返回的数据
- **更新显示逻辑**: 适配新的字段名称和数据类型

### 技术优势

1. **更稳定的依赖**: `ffprobe` 是 FFmpeg 的标准工具，比第三方 Rust 库更稳定
2. **更准确的信息**: 直接从 FFmpeg 获取信息，避免了中间层的转换误差
3. **更好的兼容性**: 支持更多视频格式和编码
4. **简化的代码结构**: 减少了复杂的数据类型处理

### 注意事项

- 需要确保系统已安装 FFmpeg（包含 ffprobe）
- 视频信息获取现在是异步操作，可能需要更长的响应时间
- 进度计算使用固定的 30fps 估算，可能与实际帧率有差异

### 测试建议

1. 测试不同格式的视频文件（MP4, MOV, AVI, MKV）
2. 验证视频信息显示的准确性
3. 测试视频处理功能的完整性
4. 检查进度显示是否正常工作 