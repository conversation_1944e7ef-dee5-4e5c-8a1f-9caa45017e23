<!-- 重构后的动作列表组件 -->
<script lang="ts">
  import { ChevronDown, ChevronRight, Search } from "lucide-svelte";

  import { onMount } from "svelte";
  import { registry, createActionTemplates } from "$lib/actions/registry";
  import { initializeActionSystem } from "$lib/actions/init";
  import { t } from "$lib/i18n";
  import type {
    ActionInstance,
    ActionTemplate,
    Category,
  } from "$lib/types/action";

  interface Props {
    actions: ActionInstance[];
    onAddAction: (template: ActionTemplate) => void;
    inputFileType: string;
  }

  let { actions, onAddAction, inputFileType }: Props = $props();

  // 状态变量
  let searchQuery = $state("");
  let expandedCategories = $state(new Set<string>());
  let availableActions = $state<ActionTemplate[]>([]);
  let categories = $state<Category[]>([]);

  // 检查动作是否已添加
  function isActionAdded(actionId: string): boolean {
    return actions.some((action) => action.actionId === actionId);
  }

  // 获取过滤后的动作
  function getFilteredActions(): ActionTemplate[] {
    if (!searchQuery.trim()) return [];

    const query = searchQuery.toLowerCase();
    return availableActions.filter(
      (action) =>
        action.name.toLowerCase().includes(query) ||
        action.description.toLowerCase().includes(query) ||
        action.category.toLowerCase().includes(query)
    );
  }

  // 获取过滤后的类别
  function getFilteredCategories(): Category[] {
    if (searchQuery.trim()) return [];
    return categories;
  }

  // 获取指定类别的动作
  function getActionsByCategory(categoryId: string): ActionTemplate[] {
    return availableActions.filter(
      (action) => action.categoryId === categoryId
    );
  }

  // 切换类别展开状态
  function toggleCategory(categoryId: string): void {
    if (expandedCategories.has(categoryId)) {
      expandedCategories.delete(categoryId);
    } else {
      expandedCategories.add(categoryId);
    }
    expandedCategories = new Set(expandedCategories);
  }

  // 处理动作添加
  function handleAddAction(template: ActionTemplate): void {
    if (!isActionAdded(template.id)) {
      onAddAction(template);
    }
  }

  // 搜索框引用
  let searchInputRef: HTMLInputElement;

  // 处理键盘快捷键
  function handleKeydown(event: KeyboardEvent): void {
    if (event.key === "F3") {
      event.preventDefault();
      // 聚焦到搜索框
      if (searchInputRef) {
        searchInputRef.focus();
        searchInputRef.select();
      }
    }
  }

  // 添加全局键盘事件监听
  onMount(() => {
    initializeActionSystem();

    // 获取类别和动作
    categories = registry.getAllCategories();
    availableActions = createActionTemplates($t);

    // 默认收起所有类别
    expandedCategories = new Set();

    // 添加键盘事件监听
    document.addEventListener("keydown", handleKeydown);

    // 清理函数
    return () => {
      document.removeEventListener("keydown", handleKeydown);
    };
  });
</script>

<!-- ActionList 容器 -->
<div class="h-full flex flex-col">
  <!-- 提示文字和搜索栏 -->
  <div class="flex-none p-3 sm:p-4 border-b border-border">
    <!-- 提示文字 -->
    <div class="mb-3 text-sm text-muted-foreground">点击动作添加到处理列表</div>
    <!-- 搜索框 -->
    <div class="relative">
      <Search
        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"
      />
      <input
        bind:this={searchInputRef}
        type="text"
        placeholder="搜索动作(F3)..."
        bind:value={searchQuery}
        class="w-full pl-10 pr-4 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background"
      />
    </div>
  </div>

  <!-- 动作列表内容区域 -->
  <div class="flex-1 overflow-y-auto p-3 sm:p-4 min-h-0">
    {#if searchQuery.trim()}
      <!-- 搜索结果显示 -->
      <div class="space-y-2">
        <h4
          class="font-medium text-sm text-muted-foreground uppercase tracking-wide"
        >
          {$t("ui.action_list.search_results")}
        </h4>
        <div class="space-y-1">
          {#each getFilteredActions() as action}
            <button
              class="w-full text-left p-2 sm:p-3 rounded-md border border-border transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98]"
              class:bg-accent={!isActionAdded(action.id)}
              class:text-accent-foreground={!isActionAdded(action.id)}
              class:hover:bg-accent={!isActionAdded(action.id)}
              class:hover:text-accent-foreground={!isActionAdded(action.id)}
              class:bg-muted={isActionAdded(action.id)}
              class:text-muted-foreground={isActionAdded(action.id)}
              class:hover:shadow-md={!isActionAdded(action.id)}
              disabled={isActionAdded(action.id)}
              onclick={() => handleAddAction(action)}
            >
              <div class="font-medium flex items-center justify-between">
                <span class="text-sm sm:text-base">{action.name}</span>
                {#if isActionAdded(action.id)}
                  <span
                    class="text-xs bg-primary text-primary-foreground px-2 py-1 rounded"
                  >
                    {$t("ui.action_list.added")}
                  </span>
                {/if}
              </div>
              <div class="text-xs sm:text-sm text-muted-foreground">
                {action.description}
              </div>
              <div class="text-xs text-muted-foreground mt-1">
                {$t("ui.action_list.category")}: {action.category}
              </div>
            </button>
          {/each}
        </div>
      </div>
    {:else}
      <!-- 分类显示 -->
      {#each getFilteredCategories() as category}
        <div class="space-y-1">
          <!-- 类别标题 -->
          <button
            class="flex items-center justify-between w-full p-2 text-left hover:bg-accent hover:text-accent-foreground rounded-md transition-colors"
            onclick={() => toggleCategory(category.id)}
          >
            <span class="font-medium text-xs sm:text-sm text-muted-foreground"
              >{$t(category.nameKey)}</span
            >
            {#if expandedCategories.has(category.id)}
              <ChevronDown class="w-4 h-4" />
            {:else}
              <ChevronRight class="w-4 h-4" />
            {/if}
          </button>

          <!-- 动作列表 -->
          {#if expandedCategories.has(category.id)}
            <div class="space-y-1">
              {#each getActionsByCategory(category.id) as action}
                <button
                  class="w-full text-left p-2 sm:p-3 rounded-md border border-border transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98]"
                  class:bg-accent={!isActionAdded(action.id)}
                  class:text-accent-foreground={!isActionAdded(action.id)}
                  class:hover:bg-accent={!isActionAdded(action.id)}
                  class:hover:text-accent-foreground={!isActionAdded(action.id)}
                  class:bg-muted={isActionAdded(action.id)}
                  class:text-muted-foreground={isActionAdded(action.id)}
                  class:hover:shadow-md={!isActionAdded(action.id)}
                  disabled={isActionAdded(action.id)}
                  onclick={() => handleAddAction(action)}
                >
                  <div class="font-medium flex items-center justify-between">
                    <span class="text-sm sm:text-base">{action.name}</span>
                    {#if isActionAdded(action.id)}
                      <span
                        class="text-xs bg-primary text-primary-foreground px-2 py-1 rounded"
                      >
                        {$t("ui.action_list.added")}
                      </span>
                    {/if}
                  </div>
                  <div class="text-xs sm:text-sm text-muted-foreground">
                    {action.description}
                  </div>
                </button>
              {/each}
            </div>
          {/if}
        </div>
      {/each}
    {/if}
  </div>
</div>
