D:\webdev\videoide\src-tauri\target\debug\VideoIDE.exe: D:\webdev\videoide\src-tauri\build.rs D:\webdev\videoide\src-tauri\capabilities D:\webdev\videoide\src-tauri\lib\ffmpeg\ffmpeg.exe D:\webdev\videoide\src-tauri\lib\ffmpeg\ffprobe.exe D:\webdev\videoide\src-tauri\lib\mpv\libmpv-2.dll D:\webdev\videoide\src-tauri\src\action_chain\commands.rs D:\webdev\videoide\src-tauri\src\action_chain\mod.rs D:\webdev\videoide\src-tauri\src\audio\mod.rs D:\webdev\videoide\src-tauri\src\color\mod.rs D:\webdev\videoide\src-tauri\src\common\mod.rs D:\webdev\videoide\src-tauri\src\config\mod.rs D:\webdev\videoide\src-tauri\src\effect\mod.rs D:\webdev\videoide\src-tauri\src\encode\mod.rs D:\webdev\videoide\src-tauri\src\filter\mod.rs D:\webdev\videoide\src-tauri\src\image\mod.rs D:\webdev\videoide\src-tauri\src\lib.rs D:\webdev\videoide\src-tauri\src\main.rs D:\webdev\videoide\src-tauri\src\player\commands.rs D:\webdev\videoide\src-tauri\src\player\mod.rs D:\webdev\videoide\src-tauri\src\player\mpv.rs D:\webdev\videoide\src-tauri\src\player\window_manager.rs D:\webdev\videoide\src-tauri\src\transform\mod.rs D:\webdev\videoide\src-tauri\src\trim\mod.rs D:\webdev\videoide\src-tauri\src\videotoimage\mod.rs D:\webdev\videoide\src-tauri\src\watermark\mod.rs D:\webdev\videoide\src-tauri\target\debug\build\VideoIDE-67a9f374cc52a1f6\out\5da590c4a87ee48c375fd1c8f79e4843cf511078d12157eedcb24e8b4605cd0b D:\webdev\videoide\src-tauri\tauri.conf.json
