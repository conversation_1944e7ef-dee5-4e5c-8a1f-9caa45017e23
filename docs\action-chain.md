# 视频动作链管理设计文档

## 概述

视频动作链是将当前组织好的动作按顺序保存起来的功能，方便下次复用，包括所有的参数设置。
比如当前添加 `截掉头部30秒` -> `添加封面图片` -> `加上背景音乐`，将这个动作链保存后，包括全部的参数设置，下次可以直接加载，选择一个新视频，点击`开始处理`就能快速应用这套动作链。

## 功能需求

### 基本功能
- **动作链管理**：保存、删除、列表、重命名
- **导入导出**：支持标准JSON格式的导入导出，包含动作链的所有信息
- **数据校验**：导入时校验格式，确保符合要求
- **冲突处理**：导入同名动作链时自动重命名（添加数字后缀）
- **路径检查**：加载动作链时检查文件路径有效性，无效路径设置为空

### 扩展功能
- **使用统计**：记录最后使用时间、使用次数等信息
- **快速操作**：支持双击加载、快捷键保存等
- **状态管理**：跟踪动作链的修改状态

## UI设计

### Tab结构
在主界面将原有的处理列表改为Tab结构：
```
┌─────────────────────────────────────────────────┐
│ [处理列表] [动作链列表]                           │
├─────────────────────────────────────────────────┤
│                                                 │
│ Tab内容区域                                      │
│                                                 │
└─────────────────────────────────────────────────┘
```

### 处理列表Tab

**顶部状态栏：**
```
┌─────────────────────────────────────────────────┐
│ 当前动作链: [我的视频处理流程] [已修改*]          │
│ [新建] [保存] [另存为] [清空]                    │
└─────────────────────────────────────────────────┘
```

**按钮功能：**
- **新建**：清空当前所有动作，状态变为"未保存"
- **保存**：保存当前动作链（新建时弹出命名对话框）
- **另存为**：复制当前动作链为新的动作链
- **清空**：清除所有动作，但保持当前动作链名称

**状态显示：**
- 显示当前动作链名称，未保存显示"未保存"
- 有修改时显示"*"标记
- Tab标题也显示未保存状态（如"处理列表*"）

### 动作链列表Tab

**顶部统计栏：**
```
┌─────────────────────────────────────────────────┐
│ 共 5 个动作链                        [导入]     │
└─────────────────────────────────────────────────┘
```

**列表项设计：**
```
┌─────────────────────────────────────────────────┐
│ 📋 我的视频处理流程                              │
│    3个动作 • 最后使用: 2024-01-15              │
│    [加载] [重命名] [导出] [删除]                 │
├─────────────────────────────────────────────────┤
│ 📋 音频提取专用                                  │
│    2个动作 • 最后使用: 2024-01-10              │
│    [加载] [重命名] [导出] [删除]                 │
└─────────────────────────────────────────────────┘
```

**按钮功能：**
- **加载**：加载动作链到处理列表并切换Tab
- **重命名**：修改动作链名称
- **导出**：将动作链导出为JSON文件
- **删除**：删除动作链（需要二次确认）
- **编辑描述**: 可以编辑该动作链的大概功能描述, 保存到JSON的`description`字段中

## 数据设计

### 存储结构（分离式设计）

**目录结构：**
```
AppData/videoide/action_chains/
├── my_workflow.json          # 动作链内容文件
├── audio_extract.json        # 动作链内容文件
├── video_compress.json       # 动作链内容文件
└── .metadata.json            # 使用统计元数据
```

**存储位置：**
- **Windows**: `C:\Users\<USER>\AppData\Roaming\videoide\action_chains\`
- **macOS**: `~/Library/Application Support/videoide/action_chains/`

### 动作链JSON格式

```json
{
  "name": "我的视频处理流程",
  "description": "常用的视频处理步骤",
  "version": "1.0",
  "actions": [
    {
      "id": "crop",
      "name": "截掉头部30秒", 
      "parameters": {
        "startTime": "00:00:30",
        "endTime": "auto"
      },
      "order": 1
    },
    {
      "id": "add_cover",
      "name": "添加封面图片",
      "parameters": {
        "imagePath": "",
        "duration": 3
      },
      "order": 2
    }
  ]
}
```

### 元数据JSON格式

```json
{
  "my_workflow.json": {
    "createdAt": "2024-01-01T00:00:00Z",
    "lastUsedAt": "2024-01-15T10:30:00Z",
    "useCount": 5
  },
  "audio_extract.json": {
    "createdAt": "2024-01-05T09:15:00Z", 
    "lastUsedAt": "2024-01-10T15:20:00Z",
    "useCount": 2
  }
}
```

## 状态管理

### 动作链状态
- **EMPTY**: 空状态，无任何动作
- **NEW**: 新建状态，有动作但未保存
- **LOADED**: 已加载动作链，无修改
- **MODIFIED**: 已加载动作链，有修改

### 状态转换
```
EMPTY → (添加动作) → NEW
NEW → (保存) → LOADED
LOADED → (修改动作) → MODIFIED
MODIFIED → (保存) → LOADED
任何状态 → (清空) → EMPTY
任何状态 → (加载动作链) → LOADED
```

## 交互流程

### 保存流程
1. 用户在处理列表中组织动作
2. 点击"保存"按钮
3. 如果是新动作链，弹出命名对话框
4. 保存到本地文件，更新元数据
5. 状态变为LOADED

### 加载流程
1. 用户在动作链列表中点击"加载"
2. 检查当前处理列表是否有未保存内容
3. 如有未保存内容，提示是否保存
4. 加载动作链内容到处理列表
5. 自动切换到处理列表Tab
6. 更新最后使用时间

### 导入导出流程
**导出：**
1. 选择动作链，点击"导出"
2. 弹出文件保存对话框
3. 保存纯净的动作链JSON文件

**导入：**
1. 点击"导入"按钮
2. 选择JSON文件
3. 校验文件格式
4. 检查同名冲突，自动重命名
5. 保存到本地目录
6. 刷新动作链列表

## 用户体验优化

### 快捷操作
- **双击动作链列表项** = 加载 + 切换Tab
- **Ctrl+S** = 快速保存当前动作链
- **Ctrl+N** = 新建动作链

### 智能提示
- 切换Tab时如有未保存内容提示保存
- 加载动作链时检查文件路径有效性并提示
- 删除动作链时二次确认
- 导入同名动作链时提供重命名选项

### 视觉反馈
- Tab标题显示未保存状态
- 动作链列表显示使用统计信息
- 保存成功后显示Toast提示
- 文件路径无效时显示警告图标

## 技术实现要点

### 后端API设计
```rust
// 动作链管理
save_action_chain(name: String, actions: Vec<Action>) -> Result<()>
load_action_chain(name: String) -> Result<ActionChain>
delete_action_chain(name: String) -> Result<()>
list_action_chains() -> Result<Vec<ActionChainInfo>>
rename_action_chain(old_name: String, new_name: String) -> Result<()>

// 导入导出
export_action_chain(name: String, path: String) -> Result<()>
import_action_chain(path: String) -> Result<String> // 返回导入后的名称

// 元数据管理
update_usage_stats(name: String) -> Result<()>
get_action_chain_stats(name: String) -> Result<UsageStats>
```

### 前端状态管理
- 使用Svelte 5的runes模式管理动作链状态
- 实现状态持久化和自动保存提醒
- 处理Tab切换时的状态同步

### 文件操作
- 使用Tauri的文件系统API进行文件读写
- 实现原子性保存，避免文件损坏
- 定期备份元数据文件

## 未来扩展

### 可选功能
- **搜索和筛选**：按名称搜索动作链
- **分类标签**：给动作链添加标签分类
- **模板系统**：提供预设动作链模板
- **云同步**：支持动作链云端同步
- **版本控制**：动作链版本管理和回滚

### 数据库升级
当动作链数量较大时，可以将元数据从JSON文件升级为SQLite数据库，但保持动作链文件格式不变，确保向后兼容。
