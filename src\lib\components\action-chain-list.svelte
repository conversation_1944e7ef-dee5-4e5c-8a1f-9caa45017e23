<script lang="ts">
  import { onMount } from "svelte";
  import { open, save } from "@tauri-apps/plugin-dialog";
  import { Card, CardContent, CardHeader } from "$lib/components/ui/card";
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Tooltip } from "$lib/components/ui/tooltip";
  import {
    Trash2,
    Edit,
    Upload,
    Download,
    FileText,
    Play,
  } from "lucide-svelte";
  import type { ActionChainInfo } from "$lib/types/action-chain";
  import {
    listActionChains,
    deleteActionChain,
    renameActionChain,
    exportActionChain,
    importActionChain,
    updateActionChainDescription,
  } from "$lib/utils/action-chain-api";

  interface Props {
    onLoadActionChain: (name: string) => void;
  }

  let { onLoadActionChain }: Props = $props();

  // 确认对话框状态
  let showDeleteConfirm = $state(false);
  let deleteTargetName = $state("");

  let actionChains: ActionChainInfo[] = $state([]);
  let loading = $state(false);
  let editingName: string | null = $state(null);
  let editingDescription: string | null = $state(null);
  let newName = $state("");
  let newDescription = $state("");

  // 加载动作链列表
  async function loadActionChainList() {
    try {
      loading = true;
      actionChains = await listActionChains();
    } catch (error) {
      console.error("加载动作链列表失败:", error);
      alert(`加载动作链列表失败: ${error}`);
    } finally {
      loading = false;
    }
  }

  // 删除动作链
  function handleDelete(name: string) {
    deleteTargetName = name;
    showDeleteConfirm = true;
  }

  // 确认删除
  async function confirmDelete() {
    try {
      await deleteActionChain(deleteTargetName);
      await loadActionChainList();
      showDeleteConfirm = false;
      deleteTargetName = "";
    } catch (error) {
      console.error("删除动作链失败:", error);
      alert(`删除动作链失败: ${error}`);
    }
  }

  // 取消删除
  function cancelDelete() {
    showDeleteConfirm = false;
    deleteTargetName = "";
  }

  // 开始重命名
  function startRename(name: string) {
    editingName = name;
    newName = name;
  }

  // 确认重命名
  async function confirmRename() {
    if (!editingName || !newName.trim() || newName === editingName) {
      editingName = null;
      return;
    }

    try {
      await renameActionChain(editingName, newName.trim());
      await loadActionChainList();
      editingName = null;
      newName = "";
    } catch (error) {
      console.error("重命名动作链失败:", error);
      alert(`重命名动作链失败: ${error}`);
    }
  }

  // 取消重命名
  function cancelRename() {
    editingName = null;
    newName = "";
  }

  // 开始编辑描述
  function startEditDescription(name: string, description: string) {
    editingDescription = name;
    newDescription = description;
  }

  // 确认编辑描述
  async function confirmEditDescription() {
    if (!editingDescription) return;

    try {
      await updateActionChainDescription(editingDescription, newDescription);
      await loadActionChainList();
      editingDescription = null;
      newDescription = "";
    } catch (error) {
      console.error("更新描述失败:", error);
      alert(`更新描述失败: ${error}`);
    }
  }

  // 取消编辑描述
  function cancelEditDescription() {
    editingDescription = null;
    newDescription = "";
  }

  // 导出动作链
  async function handleExport(name: string) {
    try {
      const filePath = await save({
        title: "选择导出位置",
        defaultPath: `${name}.json`,
        filters: [
          {
            name: "JSON文件",
            extensions: ["json"],
          },
        ],
      });

      if (filePath) {
        await exportActionChain(name, filePath as string);
        alert("导出成功！");
      }
    } catch (error) {
      console.error("导出动作链失败:", error);
      alert(`导出动作链失败: ${error}`);
    }
  }

  // 导入动作链
  async function handleImport() {
    try {
      const filePath = await open({
        title: "选择要导入的动作链文件",
        filters: [
          {
            name: "JSON文件",
            extensions: ["json"],
          },
        ],
      });

      if (filePath) {
        const importedName = await importActionChain(filePath as string);
        await loadActionChainList();
        alert(`导入成功！动作链名称: ${importedName}`);
      }
    } catch (error) {
      console.error("导入动作链失败:", error);
      alert(`导入动作链失败: ${error}`);
    }
  }

  // 格式化时间
  function formatTime(timeStr?: string): string {
    if (!timeStr) return "未使用";
    try {
      const date = new Date(timeStr);
      return (
        date.toLocaleDateString("zh-CN") +
        " " +
        date.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    } catch {
      return "未知时间";
    }
  }

  // 格式化动作预览内容
  function formatActionPreview(actionNames: string[]): string {
    if (!actionNames || actionNames.length === 0) {
      return "暂无动作";
    }

    const maxDisplay = 10; // 最多显示10个动作
    let preview = "动作预览:\n";

    const displayActions = actionNames.slice(0, maxDisplay);
    displayActions.forEach((name) => {
      preview += `• ${name}\n`;
    });

    if (actionNames.length > maxDisplay) {
      preview += `...还有${actionNames.length - maxDisplay}个动作`;
    }

    return preview.trim();
  }

  onMount(() => {
    loadActionChainList();
  });
</script>

<Card class="w-full h-full flex flex-col">
  <CardHeader class="flex-none p-4 sm:p-6">
    <div class="flex justify-between items-center">
      <div>
        <h3 class="text-lg font-semibold">动作链管理</h3>
        <p class="text-sm text-muted-foreground">
          共 {actionChains.length} 个动作链
        </p>
      </div>
      <Button onclick={handleImport} variant="outline" size="sm">
        <Download class="w-4 h-4 mr-2" />
        导入
      </Button>
    </div>
  </CardHeader>

  <CardContent class="flex-1 overflow-y-auto p-4 sm:p-6">
    {#if loading}
      <div class="flex items-center justify-center h-32 text-muted-foreground">
        <p class="text-sm">加载中...</p>
      </div>
    {:else if actionChains.length === 0}
      <div class="flex items-center justify-center h-32 text-muted-foreground">
        <div class="text-center">
          <p class="text-sm">暂无动作链</p>
          <p class="text-xs">从处理列表保存动作链或导入现有动作链</p>
        </div>
      </div>
    {:else}
      <div class="space-y-4">
        {#each actionChains as chain}
          <div class="border border-border rounded-lg p-4 space-y-3">
            <!-- 动作链名称 -->
            <div class="flex items-center justify-between">
              {#if editingName === chain.name}
                <div class="flex items-center space-x-2 flex-1">
                  <Input
                    bind:value={newName}
                    class="flex-1"
                    placeholder="输入新名称"
                    onkeydown={(e: KeyboardEvent) => {
                      if (e.key === "Enter") confirmRename();
                      if (e.key === "Escape") cancelRename();
                    }}
                  />
                  <Button size="sm" onclick={confirmRename}>确定</Button>
                  <Button size="sm" variant="outline" onclick={cancelRename}>
                    取消
                  </Button>
                </div>
              {:else}
                <Tooltip
                  content={formatActionPreview(chain.actionNames)}
                  position="bottom"
                >
                  <div class="flex items-center space-x-2">
                    <FileText class="w-5 h-5 text-muted-foreground" />
                    <h4 class="font-medium">{chain.name}</h4>
                  </div>
                </Tooltip>
              {/if}
            </div>

            <!-- 动作链描述 -->
            <div>
              {#if editingDescription === chain.name}
                <div class="space-y-2">
                  <Input
                    bind:value={newDescription}
                    placeholder="输入描述"
                    onkeydown={(e: KeyboardEvent) => {
                      if (e.key === "Enter") confirmEditDescription();
                      if (e.key === "Escape") cancelEditDescription();
                    }}
                  />
                  <div class="flex space-x-2">
                    <Button size="sm" onclick={confirmEditDescription}
                      >确定</Button
                    >
                    <Button
                      size="sm"
                      variant="outline"
                      onclick={cancelEditDescription}
                    >
                      取消
                    </Button>
                  </div>
                </div>
              {:else}
                <p
                  class="text-sm text-muted-foreground cursor-pointer hover:text-foreground"
                  on:click={() =>
                    startEditDescription(chain.name, chain.description)}
                  title="点击编辑描述"
                >
                  {chain.description || "点击添加描述..."}
                </p>
              {/if}
            </div>

            <!-- 统计信息 -->
            <div class="text-xs text-muted-foreground space-y-1">
              <p>{chain.actionCount} 个动作 • 使用 {chain.useCount} 次</p>
              <p>最后使用: {formatTime(chain.lastUsedAt)}</p>
            </div>

            <!-- 操作按钮 -->
            <div class="flex space-x-2">
              <Button size="sm" onclick={() => onLoadActionChain(chain.name)}>
                <Play class="w-4 h-4 mr-1" />
                加载
              </Button>
              <Button
                size="sm"
                variant="outline"
                onclick={() => startRename(chain.name)}
              >
                <Edit class="w-4 h-4 mr-1" />
                重命名
              </Button>
              <Button
                size="sm"
                variant="outline"
                onclick={() => handleExport(chain.name)}
              >
                <Upload class="w-4 h-4 mr-1" />
                导出
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onclick={() => handleDelete(chain.name)}
              >
                <Trash2 class="w-4 h-4 mr-1" />
                删除
              </Button>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </CardContent>
</Card>

<!-- 删除确认对话框 -->
{#if showDeleteConfirm}
  <div
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
  >
    <div
      class="bg-background rounded-lg shadow-xl p-6 min-w-[400px] max-w-[90vw]"
    >
      <h3 class="text-lg font-semibold mb-4 text-destructive">删除动作链</h3>

      <p class="text-sm text-muted-foreground mb-6">
        确定要删除动作链 <strong>"{deleteTargetName}"</strong> 吗？
        <br />
        此操作不可撤销。
      </p>

      <div class="flex justify-end gap-2">
        <Button variant="outline" onclick={cancelDelete}>取消</Button>
        <Button variant="destructive" onclick={confirmDelete}>删除</Button>
      </div>
    </div>
  </div>
{/if}
