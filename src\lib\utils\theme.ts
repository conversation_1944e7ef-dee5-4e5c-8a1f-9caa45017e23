import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// 主题类型
export type Theme = 'light' | 'dark';

// 创建主题store
function createThemeStore() {
  // 从localStorage获取初始主题，默认为light
  const getInitialTheme = (): Theme => {
    if (!browser) return 'light';
    
    const stored = localStorage.getItem('theme');
    if (stored === 'light' || stored === 'dark') {
      return stored;
    }
    
    // 检查系统偏好
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    
    return 'light';
  };

  const { subscribe, set, update } = writable<Theme>(getInitialTheme());

  return {
    subscribe,
    set: (theme: Theme) => {
      if (browser) {
        localStorage.setItem('theme', theme);
        applyTheme(theme);
      }
      set(theme);
    },
    toggle: () => {
      update(current => {
        const newTheme = current === 'light' ? 'dark' : 'light';
        if (browser) {
          localStorage.setItem('theme', newTheme);
          applyTheme(newTheme);
        }
        return newTheme;
      });
    },
    init: () => {
      if (browser) {
        const theme = getInitialTheme();
        applyTheme(theme);
        set(theme);
      }
    }
  };
}

// 应用主题到DOM
function applyTheme(theme: Theme) {
  if (!browser) return;
  
  const root = document.documentElement;
  if (theme === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
}

// 导出主题store
export const theme = createThemeStore();

// 主题图标映射
export const themeIcons = {
  light: '🌙', // 显示月亮表示可以切换到暗色
  dark: '☀️'   // 显示太阳表示可以切换到亮色
} as const;

// 主题名称映射
export const themeNames = {
  light: '亮色主题',
  dark: '暗色主题'
} as const;
