<script lang="ts">
    import { cn } from "$lib/utils";

    interface $$Props {
        value?: number;
        max?: number;
        class?: string;
    }

    let { value = 0, max = 100, class: className = "" }: $$Props = $props();
</script>

<div class={cn("w-full bg-secondary rounded-full h-2", className)}>
    <div
        class="h-full bg-primary rounded-full transition-all duration-300 ease-out"
        style="width: {Math.min((value / max) * 100, 100)}%"
    ></div>
</div>
