<script lang="ts">
  import { cn } from "$lib/utils";

  interface Props {
    class?: string;
    children?: any;
  }

  let {
    class: className,
    children,
    ...props
  }: Props = $props();
</script>

<div
  class={cn(
    "relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md",
    className
  )}
  {...props}
>
  {@render children?.()}
</div>
