use rand::Rng;
use serde::Serialize;
use std::path::{Path, PathBuf};
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// 添加封底图片 - 使用两步法避免concat文件方式的兼容性问题
#[tauri::command]
pub async fn add_end_image(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    image_path: String,
    duration: f32,
) -> Result<(), String> {
    println!("add_end_image called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  image_path: {}", image_path);
    println!("  duration: {}", duration);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入视频文件不存在".to_string());
    }

    if !Path::new(&image_path).exists() {
        return Err("输入图片文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始添加封底图片: {} -> {}", input_path, output_path);
    println!("图片文件: {}, 显示时长: {}秒", image_path, duration);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_duration = video_info.duration + duration as f64;
    let total_frames = (total_duration * 30.0) as u64; // 假设30fps用于进度计算

    // 解析视频分辨率
    let (width, height) = {
        let parts: Vec<&str> = video_info.resolution.split('x').collect();
        if parts.len() != 2 {
            return Err("无法解析视频分辨率".to_string());
        }
        (parts[0], parts[1])
    };

    // 解析帧率
    let fps = video_info.fps.split('.').next().unwrap_or("25");

    // 获取原视频的音频采样率，确保兼容性
    let audio_sample_rate = if video_info.audio_sample_rate.is_empty() {
        "48000".to_string()
    } else {
        video_info.audio_sample_rate.clone()
    };

    println!(
        "视频信息: {}x{}, fps: {}, 音频采样率: {}, 总时长: {:.2}秒",
        width, height, fps, audio_sample_rate, total_duration
    );

    // 使用更简单的方法：创建临时封底视频文件，然后用concat demuxer拼接
    let temp_end_path = format!("{}_temp_end.mp4", output_path.replace(".mp4", ""));
    println!("创建临时封底视频: {}", temp_end_path);

    // 第一步：创建封底视频
    let mut ffmpeg_end = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-loop", "1",
            "-t", &duration.to_string(),
            "-i", &image_path,
            "-f", "lavfi",
            "-i", &format!("anullsrc=channel_layout=stereo:sample_rate={}", audio_sample_rate),
            "-vf", &format!(
                "scale={}:{}:force_original_aspect_ratio=decrease,pad={}:{}:'(ow-iw)/2':'(oh-ih)/2',fps={}",
                width, height, width, height, fps
            ),
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "18",
            "-c:a", "aac",
            "-shortest",
            "-pix_fmt", "yuv420p",
            &temp_end_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg end spawn error] {e:?}");
            format!("FFmpeg封底视频创建失败: {e}")
        })?;

    // 等待封底视频创建完成
    let status = ffmpeg_end.wait().map_err(|e| {
        println!("[FFmpeg end wait error] {e:?}");
        format!("FFmpeg封底视频创建等待失败: {e}")
    })?;

    if !status.success() {
        return Err("封底视频创建失败".to_string());
    }

    println!("封底视频创建成功，开始拼接...");

    // 第二步：使用concat demuxer文件列表方式拼接，这是最可靠的方法
    let filelist_content = format!(
        "file '{}'\nfile '{}'",
        input_path.replace('\\', "/"),
        temp_end_path.replace('\\', "/")
    );

    let filelist_path = format!("{}_filelist.txt", output_path.replace(".mp4", ""));
    std::fs::write(&filelist_path, &filelist_content).map_err(|e| e.to_string())?;

    println!("创建文件列表: {}", filelist_path);
    println!("文件列表内容:\n{}", filelist_content);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            &filelist_path,
            "-c",
            "copy", // 尝试无损复制
            "-avoid_negative_ts",
            "make_zero",
            "-fflags",
            "+genpts",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg封底处理失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("IMAGE_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送处理进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg封底处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("封底图片处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;

        if pct - last_pct >= 1.0 {
            window.emit("IMAGE_PROGRESS", Payload { pct }).unwrap();
            last_pct = pct;
        }
    }

    // 等待处理完成
    let status = ffmpeg.wait().map_err(|e| {
        println!("[FFmpeg wait error] {e:?}");
        format!("FFmpeg封底处理等待失败: {e}")
    })?;

    if !status.success() {
        // 清理临时文件
        let _ = std::fs::remove_file(&temp_end_path);
        let _ = std::fs::remove_file(&filelist_path);
        return Err("封底图片处理失败".to_string());
    }

    // 清理临时文件
    let _ = std::fs::remove_file(&temp_end_path);
    let _ = std::fs::remove_file(&filelist_path);

    println!("✅ 封底图片添加完成: {}", output_path);
    window
        .emit("IMAGE_PROGRESS", Payload { pct: 100.0 })
        .unwrap();

    Ok(())
}

// 添加封面图片 - 使用两步法避免concat文件方式的兼容性问题
#[tauri::command]
pub async fn add_cover_image(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    image_path: String,
    duration: f32,
) -> Result<(), String> {
    println!("add_cover_image called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  image_path: {}", image_path);
    println!("  duration: {}", duration);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入视频文件不存在".to_string());
    }

    if !Path::new(&image_path).exists() {
        return Err("输入图片文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始添加封面图片: {} -> {}", input_path, output_path);
    println!("图片文件: {}, 显示时长: {}秒", image_path, duration);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_duration = video_info.duration + duration as f64;
    let total_frames = (total_duration * 30.0) as u64; // 假设30fps用于进度计算

    // 解析视频分辨率
    let (width, height) = {
        let parts: Vec<&str> = video_info.resolution.split('x').collect();
        if parts.len() != 2 {
            return Err("无法解析视频分辨率".to_string());
        }
        (parts[0], parts[1])
    };

    // 解析帧率
    let fps = video_info.fps.split('.').next().unwrap_or("25");

    // 获取原视频的音频采样率，确保兼容性
    let audio_sample_rate = if video_info.audio_sample_rate.is_empty() {
        "48000".to_string()
    } else {
        video_info.audio_sample_rate.clone()
    };

    println!(
        "视频信息: {}x{}, fps: {}, 音频采样率: {}, 总时长: {:.2}秒",
        width, height, fps, audio_sample_rate, total_duration
    );

    // 使用两步法：创建临时封面视频文件，然后用concat demuxer拼接
    // 这样可以避免复杂的filter_complex语法问题，确保最佳兼容性
    let temp_cover_path = format!("{}_temp_cover.mp4", output_path.replace(".mp4", ""));
    println!("创建临时封面视频: {}", temp_cover_path);

    // 第一步：创建封面视频
    let mut ffmpeg_cover = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-loop", "1",
            "-t", &duration.to_string(),
            "-i", &image_path,
            "-f", "lavfi",
            "-i", &format!("anullsrc=channel_layout=stereo:sample_rate={}", audio_sample_rate),
            "-vf", &format!(
                "scale={}:{}:force_original_aspect_ratio=decrease,pad={}:{}:'(ow-iw)/2':'(oh-ih)/2',fps={}",
                width, height, width, height, fps
            ),
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "18",
            "-c:a", "aac",
            "-shortest",
            "-pix_fmt", "yuv420p",
            &temp_cover_path,
        ])
        .print_command()
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg cover spawn error] {e:?}");
            format!("FFmpeg封面视频创建失败: {e}")
        })?;

    // 等待封面视频创建完成
    let status = ffmpeg_cover.wait().map_err(|e| {
        println!("[FFmpeg cover wait error] {e:?}");
        format!("FFmpeg封面视频创建等待失败: {e}")
    })?;

    if !status.success() {
        return Err("封面视频创建失败".to_string());
    }

    println!("封面视频创建成功，开始拼接...");

    // 第二步：使用concat demuxer文件列表方式拼接，这是最可靠的方法
    let filelist_content = format!(
        "file '{}'\nfile '{}'",
        temp_cover_path.replace('\\', "/"),
        input_path.replace('\\', "/")
    );

    let filelist_path = format!("{}_filelist.txt", output_path.replace(".mp4", ""));
    std::fs::write(&filelist_path, &filelist_content).map_err(|e| e.to_string())?;

    println!("创建文件列表: {}", filelist_path);
    println!("文件列表内容:\n{}", filelist_content);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            &filelist_path,
            "-c",
            "copy", // 尝试无损复制
            "-avoid_negative_ts",
            "make_zero",
            "-fflags",
            "+genpts",
            &output_path,
        ])
        .print_command()
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg封面拼接失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("IMAGE_PROGRESS", Payload { pct: 50.0 })
        .unwrap();

    // 推送拼接进度
    let mut last_pct = 50.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg封面拼接失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("封面图片处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = (50.0 + (frame as f64 * 50.0) / total_frames as f64).min(100.0) as f32;

        if pct - last_pct >= 1.0 {
            window.emit("IMAGE_PROGRESS", Payload { pct }).unwrap();
            last_pct = pct;
        }
    }

    // 等待拼接完成
    let status = ffmpeg.wait().map_err(|e| {
        println!("[FFmpeg wait error] {e:?}");
        format!("FFmpeg封面拼接等待失败: {e}")
    })?;

    if !status.success() {
        // 清理临时文件
        let _ = std::fs::remove_file(&temp_cover_path);
        let _ = std::fs::remove_file(&filelist_path);
        return Err("封面图片拼接失败".to_string());
    }

    // 清理临时文件
    let _ = std::fs::remove_file(&temp_cover_path);
    let _ = std::fs::remove_file(&filelist_path);

    println!("✅ 封面图片添加完成: {}", output_path);
    window
        .emit("IMAGE_PROGRESS", Payload { pct: 100.0 })
        .unwrap();

    Ok(())
}

// 内部函数：单张图片转视频（支持进度回调）
async fn create_video_from_image_with_progress(
    window: &tauri::Window,
    input_path: &str,
    output_path: &str,
    duration: f32,
    width: i32,
    height: i32,
    base_progress: f32,
    progress_weight: f32,
) -> Result<(), String> {
    // 检查输入文件是否存在
    if !Path::new(input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 使用保持宽高比的缩放策略，在指定分辨率内居中显示，空白区域填充黑色
    let scale_filter = format!(
        "scale={}:{}:force_original_aspect_ratio=decrease,pad={}:{}:(ow-iw)/2:(oh-ih)/2:black",
        width, height, width, height
    );

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-loop",
            "1",
            "-i",
            input_path,
            "-f",
            "lavfi",
            "-i",
            "anullsrc=channel_layout=stereo:sample_rate=48000", // 生成静音音频轨道
            "-c:v",
            "libx264",
            "-c:a",
            "aac", // 音频编码器
            "-t",
            &duration.to_string(),
            "-pix_fmt",
            "yuv420p",
            "-vf",
            &scale_filter,
            "-shortest", // 以最短的流为准（确保音视频同步）
            "-y",
            output_path,
        ])
        .spawn()
        .map_err(|e| {
            eprintln!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 估算总帧数（基于时长和帧率）
    let estimated_frames = (duration * 25.0) as f32; // 假设25fps
    let mut last_pct = base_progress;

    // 监控进度并发送事件
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            return Err("处理已取消".to_string());
        }

        // 计算当前图片的进度
        let frame_progress = (progress.frame as f32 / estimated_frames).min(1.0);
        let current_pct = base_progress + (frame_progress * progress_weight);

        // 只在进度有明显变化时发送事件
        if current_pct - last_pct >= 0.5 {
            window
                .emit("BATCH_IMAGE_PROGRESS", Payload { pct: current_pct })
                .unwrap();
            last_pct = current_pct;
        }
    }

    // 检查ffmpeg进程退出状态
    let status = ffmpeg
        .wait()
        .map_err(|e| format!("FFmpeg进程等待失败: {}", e))?;

    if !status.success() {
        return Err("图片转视频处理失败".to_string());
    }

    // 确保当前图片处理完成时发送完整进度
    let final_pct = base_progress + progress_weight;
    window
        .emit("BATCH_IMAGE_PROGRESS", Payload { pct: final_pct })
        .unwrap();

    Ok(())
}

// 内部函数：单张图片转视频（不发送进度事件，保持向后兼容）
async fn create_video_from_image(
    window: &tauri::Window,
    input_path: &str,
    output_path: &str,
    duration: f32,
    width: i32,
    height: i32,
) -> Result<(), String> {
    create_video_from_image_with_progress(
        window,
        input_path,
        output_path,
        duration,
        width,
        height,
        0.0,
        0.0,
    )
    .await
}

#[derive(Serialize)]
pub struct FolderInfo {
    pub files: Vec<String>,
    pub total_size_mb: f64,
    pub folder_type: String, // "image", "video", "audio", "mixed"
    pub file_counts: FileTypeCounts,
}

#[derive(Serialize)]
pub struct FileTypeCounts {
    pub image_count: usize,
    pub video_count: usize,
    pub audio_count: usize,
    pub other_count: usize,
}

// 获取文件夹信息（包括文件列表、大小和类型）
#[tauri::command]
pub async fn get_folder_info(folder_path: String) -> Result<FolderInfo, String> {
    // 检查输入文件夹是否存在
    if !Path::new(&folder_path).exists() {
        return Err("输入文件夹不存在".to_string());
    }

    let image_extensions = ["jpg", "jpeg", "png", "bmp", "gif", "webp"];
    let video_extensions = ["mp4", "avi", "mov", "mkv", "wmv", "flv", "webm", "m4v"];
    let audio_extensions = ["mp3", "wav", "aac", "flac", "m4a", "ogg", "wma"];

    let mut all_files = Vec::new();
    let mut total_size_bytes = 0u64;
    let mut image_count = 0;
    let mut video_count = 0;
    let mut audio_count = 0;
    let mut other_count = 0;

    let entries = std::fs::read_dir(&folder_path).map_err(|e| format!("读取文件夹失败: {}", e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("读取文件失败: {}", e))?;
        let path = entry.path();

        if path.is_file() {
            // 获取文件大小
            if let Ok(metadata) = path.metadata() {
                total_size_bytes += metadata.len();
            }

            if let Some(extension) = path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    let ext_lower = ext_str.to_lowercase();

                    if image_extensions.contains(&ext_lower.as_str()) {
                        image_count += 1;
                        if let Some(file_name) = path.file_name() {
                            if let Some(name_str) = file_name.to_str() {
                                all_files.push(name_str.to_string());
                            }
                        }
                    } else if video_extensions.contains(&ext_lower.as_str()) {
                        video_count += 1;
                        if let Some(file_name) = path.file_name() {
                            if let Some(name_str) = file_name.to_str() {
                                all_files.push(name_str.to_string());
                            }
                        }
                    } else if audio_extensions.contains(&ext_lower.as_str()) {
                        audio_count += 1;
                        if let Some(file_name) = path.file_name() {
                            if let Some(name_str) = file_name.to_str() {
                                all_files.push(name_str.to_string());
                            }
                        }
                    } else {
                        other_count += 1;
                    }
                }
            } else {
                other_count += 1;
            }
        }
    }

    // 按文件名排序
    all_files.sort();

    // 计算文件夹类型（按主要文件类型判断）
    let total_media_files = image_count + video_count + audio_count;
    let folder_type = if total_media_files == 0 {
        "empty".to_string()
    } else if image_count > video_count && image_count > audio_count {
        "image".to_string()
    } else if video_count > image_count && video_count > audio_count {
        "video".to_string()
    } else if audio_count > image_count && audio_count > video_count {
        "audio".to_string()
    } else {
        "mixed".to_string()
    };

    let total_size_mb = total_size_bytes as f64 / 1024.0 / 1024.0;

    Ok(FolderInfo {
        files: all_files,
        total_size_mb,
        folder_type,
        file_counts: FileTypeCounts {
            image_count,
            video_count,
            audio_count,
            other_count,
        },
    })
}

// 获取文件夹中的图片文件列表（保持向后兼容）
#[tauri::command]
pub async fn get_image_files_in_folder(folder_path: String) -> Result<Vec<String>, String> {
    let folder_info = get_folder_info(folder_path).await?;

    // 只返回图片文件
    let image_extensions = ["jpg", "jpeg", "png", "bmp", "gif", "webp"];
    let image_files: Vec<String> = folder_info
        .files
        .into_iter()
        .filter(|file| {
            if let Some(ext) = file.split('.').last() {
                image_extensions.contains(&ext.to_lowercase().as_str())
            } else {
                false
            }
        })
        .collect();

    Ok(image_files)
}

// 批量图片转视频
#[tauri::command]
pub async fn batch_images_to_video(
    window: tauri::Window,
    folder_path: String,
    output_path: String,
    duration_mode: String, // "total" 或 "per_image"
    duration_value: f32,
    width: i32,
    height: i32,
    transition_type: String,  // 切换效果类型
    transition_duration: f32, // 切换时长
) -> Result<(), String> {
    println!("batch_images_to_video called with:");
    println!("  folder_path: {}", folder_path);
    println!("  output_path: {}", output_path);
    println!("  duration_mode: {}", duration_mode);
    println!("  duration_value: {}", duration_value);
    println!("  width: {}", width);
    println!("  height: {}", height);
    println!("  transition_type: {}", transition_type);
    println!("  transition_duration: {}", transition_duration);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件夹是否存在
    if !Path::new(&folder_path).exists() {
        return Err("输入文件夹不存在".to_string());
    }

    // 扫描文件夹中的图片文件
    let image_extensions = ["jpg", "jpeg", "png", "bmp", "gif", "webp"];
    let mut image_files = Vec::new();

    let entries = std::fs::read_dir(&folder_path).map_err(|e| format!("读取文件夹失败: {}", e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("读取文件失败: {}", e))?;
        let path = entry.path();

        if path.is_file() {
            if let Some(extension) = path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    if image_extensions.contains(&ext_str.to_lowercase().as_str()) {
                        image_files.push(path);
                    }
                }
            }
        }
    }

    if image_files.is_empty() {
        return Err("文件夹中没有找到图片文件".to_string());
    }

    // 按文件名排序
    image_files.sort();

    println!("找到 {} 个图片文件", image_files.len());

    // 根据切换效果类型选择不同的处理方式
    if transition_type == "none" {
        // 无切换效果，使用原有的简单合并方式
        return batch_images_to_video_simple(
            &window,
            &image_files,
            &output_path,
            duration_mode,
            duration_value,
            width,
            height,
        )
        .await;
    } else {
        // 有切换效果，使用复杂滤镜方式
        return batch_images_to_video_with_transitions(
            &window,
            &image_files,
            &output_path,
            duration_mode,
            duration_value,
            width,
            height,
            &transition_type,
            transition_duration,
        )
        .await;
    }
}

// 简单的批量图片转视频（无切换效果）
async fn batch_images_to_video_simple(
    window: &tauri::Window,
    image_files: &[PathBuf],
    output_path: &str,
    duration_mode: String,
    duration_value: f32,
    width: i32,
    height: i32,
) -> Result<(), String> {
    let _total_images = image_files.len();

    // 确保宽度和高度为偶数（H.264编码器要求）
    let original_width = width;
    let original_height = height;
    let (width, height) = ensure_even_dimensions(width, height);
    if width != original_width || height != original_height {
        println!(
            "📐 调整分辨率为偶数: {}x{} -> {}x{}",
            original_width, original_height, width, height
        );
    }

    // 计算每张图片的显示时长
    let per_image_duration = match duration_mode.as_str() {
        "total" => duration_value / image_files.len() as f32,
        "per_image" => duration_value,
        _ => return Err("无效的时长模式".to_string()),
    };

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 创建临时文件列表
    let temp_dir = std::env::temp_dir();
    let temp_list_file = temp_dir.join(format!(
        "batch_images_{}.txt",
        rand::thread_rng().gen::<u32>()
    ));
    let mut file_list_content = String::new();

    // 为每张图片创建临时视频文件
    let mut temp_video_files = Vec::new();
    let total_images = image_files.len();

    for (index, image_path) in image_files.iter().enumerate() {
        if is_cancelled() {
            println!("批量图片转视频已取消");
            // 清理临时文件
            for temp_file in &temp_video_files {
                let _ = std::fs::remove_file(temp_file);
            }
            return Err("处理已取消".to_string());
        }

        let temp_video_path = temp_dir.join(format!(
            "temp_video_{}_{}.mp4",
            rand::thread_rng().gen::<u32>(),
            index
        ));

        // 计算当前图片的进度范围
        let base_progress = (index as f32 / total_images as f32) * 80.0;
        let progress_weight = (1.0 / total_images as f32) * 80.0;

        // 调用带进度的单张图片转视频功能
        let result = create_video_from_image_with_progress(
            &window,
            image_path.to_str().unwrap(),
            temp_video_path.to_str().unwrap(),
            per_image_duration,
            width,
            height,
            base_progress,
            progress_weight,
        )
        .await;

        if let Err(e) = result {
            // 清理已创建的临时文件
            for temp_file in &temp_video_files {
                let _ = std::fs::remove_file(temp_file);
            }
            return Err(format!("处理图片 {} 失败: {}", image_path.display(), e));
        }

        temp_video_files.push(temp_video_path.clone());
        file_list_content.push_str(&format!("file '{}'\n", temp_video_path.to_str().unwrap()));
    }

    // 写入文件列表
    std::fs::write(&temp_list_file, &file_list_content).map_err(|e| e.to_string())?;

    // 使用 FFmpeg 合并所有临时视频
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            temp_list_file.to_str().unwrap(),
            "-c",
            "copy",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg合并处理失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 等待合并完成
    let mut last_pct = 80.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg合并处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("批量图片转视频合并已取消");
            kill_ffmpeg_process();
            // 清理临时文件
            for temp_file in &temp_video_files {
                let _ = std::fs::remove_file(temp_file);
            }
            let _ = std::fs::remove_file(&temp_list_file);
            return Err("处理已取消".to_string());
        }

        let pct = 80.0 + (progress.frame as f64 * 20.0 / 100.0).min(20.0) as f32;
        if pct - last_pct >= 1.0 {
            window
                .emit("BATCH_IMAGE_PROGRESS", Payload { pct })
                .unwrap();
            last_pct = pct;
        }
    }

    // 等待处理完成
    let status = ffmpeg.wait().map_err(|e| {
        println!("[FFmpeg wait error] {e:?}");
        format!("FFmpeg合并处理等待失败: {e}")
    })?;

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    // 清理临时文件
    for temp_file in &temp_video_files {
        let _ = std::fs::remove_file(temp_file);
    }
    let _ = std::fs::remove_file(&temp_list_file);

    if !status.success() {
        return Err("FFmpeg合并处理失败".to_string());
    }

    println!("批量图片转视频完成: {}", output_path);
    Ok(())
}

// 确保尺寸为偶数（H.264编码器要求）
fn ensure_even_dimensions(width: i32, height: i32) -> (i32, i32) {
    let even_width = if width % 2 == 0 { width } else { width + 1 };
    let even_height = if height % 2 == 0 { height } else { height + 1 };
    (even_width, even_height)
}

// 带切换效果的批量图片转视频
async fn batch_images_to_video_with_transitions(
    window: &tauri::Window,
    image_files: &[PathBuf],
    output_path: &str,
    duration_mode: String,
    duration_value: f32,
    width: i32,
    height: i32,
    transition_type: &str,
    transition_duration: f32,
) -> Result<(), String> {
    let total_images = image_files.len();

    // 确保宽度和高度为偶数（H.264编码器要求）
    let original_width = width;
    let original_height = height;
    let (width, height) = ensure_even_dimensions(width, height);
    if width != original_width || height != original_height {
        println!(
            "📐 调整分辨率为偶数: {}x{} -> {}x{}",
            original_width, original_height, width, height
        );
    }

    if total_images < 2 {
        // 如果只有一张图片，回退到简单模式
        return batch_images_to_video_simple(
            window,
            image_files,
            output_path,
            duration_mode,
            duration_value,
            width,
            height,
        )
        .await;
    }

    // 计算每张图片的显示时长
    let per_image_duration = match duration_mode.as_str() {
        "total" => {
            // 总时长需要减去所有切换效果的时长
            let total_transition_time = (total_images - 1) as f32 * transition_duration;
            let remaining_time = duration_value - total_transition_time;
            if remaining_time <= 0.0 {
                return Err("总时长太短，无法容纳所有切换效果".to_string());
            }
            remaining_time / total_images as f32
        }
        "per_image" => duration_value,
        _ => return Err("无效的时长模式".to_string()),
    };

    println!(
        "每张图片显示时长: {} 秒, 切换时长: {} 秒",
        per_image_duration, transition_duration
    );

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 创建临时目录（在图片目录下）
    let temp_dir = if let Some(parent) = image_files[0].parent() {
        parent.join("temp_video_segments")
    } else {
        std::env::temp_dir().join("temp_video_segments")
    };
    std::fs::create_dir_all(&temp_dir).map_err(|e| e.to_string())?;

    println!("临时目录: {:?}", temp_dir);

    // 发送初始进度
    window
        .emit("BATCH_IMAGE_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    let mut temp_video_files = Vec::new();

    // 为每张图片创建视频片段
    for i in 0..total_images {
        if is_cancelled() {
            println!("批量图片转视频已取消");
            // 清理临时文件
            for temp_file in &temp_video_files {
                let _ = std::fs::remove_file(temp_file);
            }
            return Err("处理已取消".to_string());
        }

        let temp_video_path = temp_dir.join(format!("segment_{:03}.mp4", i));

        if i == total_images - 1 {
            // 最后一张图片：只显示，不切换
            println!("创建最后一段 {}: 显示{}秒", i + 1, per_image_duration);

            create_single_image_segment(
                window,
                &image_files[i],
                &temp_video_path,
                per_image_duration,
                width,
                height,
            )
            .await?;
        } else {
            // 前面的图片：显示 + 切换到下一张
            println!(
                "创建第{}段: 显示{}秒 + {}秒切换到第{}页",
                i + 1,
                per_image_duration,
                transition_duration,
                i + 2
            );

            create_transition_segment(
                window,
                &image_files[i],
                &image_files[i + 1],
                &temp_video_path,
                per_image_duration,
                transition_duration,
                width,
                height,
                transition_type,
            )
            .await?;
        }

        temp_video_files.push(temp_video_path);

        // 发送进度更新
        let progress = ((i + 1) as f32 / total_images as f32) * 90.0; // 90% 用于创建片段
        window
            .emit("BATCH_IMAGE_PROGRESS", Payload { pct: progress })
            .unwrap();
    }

    // 拼接所有视频片段
    println!("🔗 开始拼接{}个视频片段...", temp_video_files.len());

    concat_video_segments(window, &temp_video_files, output_path).await?;

    // 清理临时文件
    println!("🧹 清理临时文件...");
    for temp_file in &temp_video_files {
        let _ = std::fs::remove_file(temp_file);
    }
    let _ = std::fs::remove_dir(&temp_dir);

    // 发送完成进度
    window
        .emit("BATCH_IMAGE_PROGRESS", Payload { pct: 100.0 })
        .unwrap();

    println!("✅ 带切换效果的批量图片转视频处理完成！");
    Ok(())
}

// 创建单张图片的视频片段（无切换效果）
async fn create_single_image_segment(
    _window: &tauri::Window,
    image_path: &PathBuf,
    output_path: &PathBuf,
    duration: f32,
    width: i32,
    height: i32,
) -> Result<(), String> {
    // 使用保持宽高比的缩放策略，在指定分辨率内居中显示，空白区域填充黑色
    let scale_filter = format!(
        "scale={}:{}:force_original_aspect_ratio=decrease,pad={}:{}:(ow-iw)/2:(oh-ih)/2:black",
        width, height, width, height
    );

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-loop",
            "1",
            "-i",
            image_path.to_str().unwrap(),
            "-f",
            "lavfi",
            "-i",
            "anullsrc=channel_layout=stereo:sample_rate=48000",
            "-c:v",
            "libx264",
            "-c:a",
            "aac",
            "-t",
            &duration.to_string(),
            "-pix_fmt",
            "yuv420p",
            "-vf",
            &scale_filter,
            "-shortest",
            output_path.to_str().unwrap(),
        ])
        .spawn()
        .map_err(|e| format!("创建单张图片片段失败: {}", e))?;

    // 等待处理完成
    let status = ffmpeg
        .wait()
        .map_err(|e| format!("等待单张图片片段处理失败: {}", e))?;

    if !status.success() {
        return Err(format!("单张图片片段处理失败: {:?}", status.code()));
    }

    Ok(())
}

// 创建带切换效果的视频片段
async fn create_transition_segment(
    _window: &tauri::Window,
    image1_path: &PathBuf,
    image2_path: &PathBuf,
    output_path: &PathBuf,
    display_duration: f32,
    transition_duration: f32,
    width: i32,
    height: i32,
    transition_type: &str,
) -> Result<(), String> {
    let transition_param = match transition_type {
        "fade" => "fade",
        "slideleft" => "slideleft",
        "slideright" => "slideright",
        _ => "fade",
    };

    // 重新理解xfade的工作原理：
    // xfade的输出长度 = max(第一个输入长度, offset + transition_duration + 第二个输入剩余长度)
    // 我们希望输出长度正好是 display_duration + transition_duration
    //
    // 设置：
    // - 第一个输入长度 = display_duration + transition_duration (足够长以支持切换)
    // - 第二个输入长度 = display_duration + transition_duration (足够长)
    // - offset = display_duration (在显示完成后开始切换)
    // - 输出会被trim到正确长度

    let input_duration = display_duration + transition_duration;
    let expected_output_duration = display_duration + transition_duration;

    // 使用保持宽高比的缩放策略
    let filter_complex = format!(
        "[0:v]scale={}:{}:force_original_aspect_ratio=decrease,pad={}:{}:(ow-iw)/2:(oh-ih)/2:black[v0];[1:v]scale={}:{}:force_original_aspect_ratio=decrease,pad={}:{}:(ow-iw)/2:(oh-ih)/2:black[v1];[v0][v1]xfade=transition={}:duration={}:offset={}[xfaded];[xfaded]trim=duration={}[out]",
        width, height, width, height, width, height, width, height, transition_param, transition_duration, display_duration, expected_output_duration
    );

    println!(
        "  输入时长: {}秒, 预期输出时长: {}秒",
        input_duration, expected_output_duration
    );

    println!("🎬 创建切换片段:");
    println!("  图片1: {}", image1_path.display());
    println!("  图片2: {}", image2_path.display());
    println!("  输出: {}", output_path.display());
    println!(
        "  显示时长: {}秒, 切换时长: {}秒, 总时长: {}秒",
        display_duration, transition_duration, expected_output_duration
    );
    println!("  滤镜: {}", filter_complex);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-loop",
            "1",
            "-t",
            &input_duration.to_string(),
            "-i",
            image1_path.to_str().unwrap(),
            "-loop",
            "1",
            "-t",
            &input_duration.to_string(),
            "-i",
            image2_path.to_str().unwrap(),
            "-f",
            "lavfi",
            "-i",
            "anullsrc=channel_layout=stereo:sample_rate=48000",
            "-filter_complex",
            &filter_complex,
            "-map",
            "[out]",
            "-map",
            "2:a",
            "-c:v",
            "libx264",
            "-c:a",
            "aac",
            "-pix_fmt",
            "yuv420p",
            "-shortest",
            output_path.to_str().unwrap(),
        ])
        .spawn()
        .map_err(|e| {
            println!("❌ 创建切换片段FFmpeg启动失败: {}", e);
            format!("创建切换片段失败: {}", e)
        })?;

    // 监控FFmpeg输出
    let ffmpeg_iter = ffmpeg.iter().map_err(|e| {
        println!("❌ FFmpeg iter失败: {}", e);
        format!("FFmpeg处理失败: {}", e)
    })?;

    for event in ffmpeg_iter {
        match event {
            ffmpeg_sidecar::event::FfmpegEvent::Log(log_level, content) => {
                println!("[切换片段 FFmpeg Log] {:?}: {}", log_level, content);

                // 检查错误
                if content.contains("Error")
                    || content.contains("error")
                    || content.contains("ERROR")
                    || content.contains("failed")
                    || content.contains("Invalid")
                    || content.contains("Cannot")
                {
                    println!("🔴 [切换片段 ERROR] {}", content);
                }
            }
            ffmpeg_sidecar::event::FfmpegEvent::LogEOF => {
                println!("[切换片段] FFmpeg日志结束");
            }
            _ => {}
        }
    }

    // 等待处理完成
    let status = ffmpeg.wait().map_err(|e| {
        println!("❌ 等待切换片段处理失败: {}", e);
        format!("等待切换片段处理失败: {}", e)
    })?;

    println!("📊 切换片段FFmpeg退出状态: {:?}", status);
    if !status.success() {
        println!("❌ 切换片段处理失败，退出代码: {:?}", status.code());
        return Err(format!("切换片段处理失败: {:?}", status.code()));
    }

    println!("✅ 切换片段创建成功: {}", output_path.display());

    Ok(())
}

// 拼接视频片段
async fn concat_video_segments(
    _window: &tauri::Window,
    video_files: &[PathBuf],
    output_path: &str,
) -> Result<(), String> {
    // 创建文件列表
    let temp_list_file = std::env::temp_dir().join("video_segments_list.txt");
    let mut file_list_content = String::new();

    for video_file in video_files {
        file_list_content.push_str(&format!("file '{}'\n", video_file.to_str().unwrap()));
    }

    std::fs::write(&temp_list_file, &file_list_content).map_err(|e| e.to_string())?;

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            temp_list_file.to_str().unwrap(),
            "-c",
            "copy",
            output_path,
        ])
        .spawn()
        .map_err(|e| format!("拼接视频失败: {}", e))?;

    // 等待处理完成
    let status = ffmpeg
        .wait()
        .map_err(|e| format!("等待拼接完成失败: {}", e))?;

    // 清理临时文件列表
    let _ = std::fs::remove_file(&temp_list_file);

    if !status.success() {
        return Err(format!("视频拼接失败: {:?}", status.code()));
    }

    Ok(())
}

// 图片转视频
#[tauri::command]
pub async fn image_to_video(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    duration: f32,
    width: i32,
    height: i32,
) -> Result<(), String> {
    // 确保宽度和高度为偶数（H.264编码器要求）
    let original_width = width;
    let original_height = height;
    let (width, height) = ensure_even_dimensions(width, height);
    if width != original_width || height != original_height {
        println!(
            "📐 调整分辨率为偶数: {}x{} -> {}x{}",
            original_width, original_height, width, height
        );
    }
    println!("image_to_video called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  duration: {}", duration);
    println!("  width: {}", width);
    println!("  height: {}", height);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始处理图片转视频: {} -> {}", input_path, output_path);
    println!("时长: {:.2} 秒, 分辨率: {}x{}", duration, width, height);

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("IMAGE_TO_VIDEO_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 使用保持宽高比的缩放策略，在指定分辨率内居中显示，空白区域填充黑色
    let scale_filter = format!(
        "scale={}:{}:force_original_aspect_ratio=decrease,pad={}:{}:(ow-iw)/2:(oh-ih)/2:black",
        width, height, width, height
    );

    use std::time::{Duration as StdDuration, Instant};
    let start_time = Instant::now();
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-loop",
            "1",
            "-i",
            &input_path,
            "-f",
            "lavfi",
            "-i",
            "anullsrc=channel_layout=stereo:sample_rate=48000", // 生成静音音频轨道
            "-c:v",
            "libx264",
            "-c:a",
            "aac", // 音频编码器
            "-t",
            &duration.to_string(),
            "-pix_fmt",
            "yuv420p",
            "-vf",
            &scale_filter,
            "-shortest", // 以最短的流为准（确保音视频同步）
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            eprintln!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    let mut last_pct = 0.0f32;
    let mut manual_tick = 0;
    let mut has_real_progress = false;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            eprintln!("图片转视频处理已取消");
            mark_ffmpeg_finished();
            return Err("处理已取消".to_string());
        }
        let time_ms: f32 = progress.time.parse::<f32>().unwrap_or(0.0);
        let pct = (time_ms / (duration * 1000.0)) * 100.0;
        if pct > 0.0 {
            has_real_progress = true;
        }
        // 兜底：如果 pct 一直为 0，手动模拟进度
        if !has_real_progress && manual_tick < 10 {
            let elapsed = start_time.elapsed().as_secs_f32();
            let fake_pct = ((elapsed / 10.0).min(1.0)) * 99.0; // 10秒内推到99%
            if fake_pct - last_pct >= 5.0 {
                last_pct = fake_pct;
                window
                    .emit("IMAGE_TO_VIDEO_PROGRESS", Payload { pct: fake_pct })
                    .unwrap();
                manual_tick += 1;
            }
            std::thread::sleep(StdDuration::from_millis(500));
        }
        if pct - last_pct >= 1.0 {
            last_pct = pct;
            window
                .emit("IMAGE_TO_VIDEO_PROGRESS", Payload { pct })
                .unwrap();
        }
    }

    // 检查ffmpeg进程退出状态
    let status = ffmpeg
        .wait()
        .map_err(|e| format!("FFmpeg进程等待失败: {}", e))?;
    mark_ffmpeg_finished();

    // 兜底：如果没有真实进度，最后强制推送100%
    window
        .emit("IMAGE_TO_VIDEO_PROGRESS", Payload { pct: 100.0 })
        .unwrap();

    if !status.success() {
        return Err("图片转视频处理失败".to_string());
    }
    eprintln!("✅ 图片转视频处理完成: {}", output_path);
    Ok(())
}
