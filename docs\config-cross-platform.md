# Config模块跨平台支持

## 概述

config模块已经更新以支持Windows和macOS的路径差异，确保FFmpeg和FFprobe可执行文件能够在不同操作系统上正确找到。

## 主要修改

### 1. 可执行文件扩展名处理

- **Windows**: 使用 `.exe` 扩展名
- **macOS/Linux**: 不使用扩展名

```rust
let exe_extension = if cfg!(target_os = "windows") {
    ".exe"
} else {
    "" // macOS和Linux不需要扩展名
};
```

### 2. FFmpeg库路径结构

#### Windows
```
{base_path}/lib/ffmpeg/ffmpeg.exe
{base_path}/lib/ffmpeg/ffprobe.exe
```

#### macOS
```
{base_path}/Contents/Resources/ffmpeg/ffmpeg
{base_path}/Contents/Resources/ffmpeg/ffprobe
```

#### Linux
```
{base_path}/lib/ffmpeg/ffmpeg
{base_path}/lib/ffmpeg/ffprobe
```

### 3. macOS .app Bundle支持

在macOS上，应用程序通常打包为 `.app` bundle。config模块现在能够：

1. 检测可执行文件是否在 `.app` bundle内
2. 自动向上查找 `.app` bundle路径
3. 使用正确的 `Contents/Resources` 路径

```rust
#[cfg(target_os = "macos")]
fn find_app_bundle_path(exe_dir: &Path) -> Option<PathBuf> {
    let mut current = exe_dir;
    
    // 向上查找.app bundle
    while let Some(parent) = current.parent() {
        if current.file_name()
            .and_then(|n| n.to_str())
            .map(|s| s.ends_with(".app"))
            .unwrap_or(false)
        {
            return Some(current.to_path_buf());
        }
        current = parent;
    }
    
    None
}
```

## 开发环境 vs 生产环境

### 开发环境
- 在 `src-tauri` 目录下查找FFmpeg文件
- 支持多种查找策略以确保开发时的灵活性

### 生产环境
- Windows: 使用可执行文件所在目录
- macOS: 自动检测 `.app` bundle并使用 `Contents/Resources` 路径
- Linux: 使用可执行文件所在目录

## 测试

模块包含自动化测试来验证跨平台功能：

- `test_os_specific_paths`: 验证不同操作系统下的路径构建
- `test_app_bundle_detection`: 验证macOS上的 `.app` bundle检测

运行测试：
```bash
cargo test config::tests
```

## 使用示例

```rust
// 初始化配置（会自动处理平台差异）
config::init_ffmpeg_config()?;

// 获取FFmpeg路径
let ffmpeg_path = config::get_ffmpeg_path()?;

// 创建FFmpeg命令
let mut cmd = config::create_ffmpeg_command()?;
```

## 注意事项

1. **路径分隔符**: 代码使用Rust的 `PathBuf` 自动处理不同操作系统的路径分隔符
2. **条件编译**: 使用 `cfg!` 宏进行条件编译，确保只在相关平台上编译相关代码
3. **错误处理**: 提供详细的错误信息，帮助诊断路径问题
4. **日志输出**: 在初始化时输出实际使用的路径，便于调试

## 未来扩展

如果需要支持其他平台（如Linux），可以轻松扩展：

```rust
} else if cfg!(target_os = "linux") {
    // Linux特定路径
    base_path.join("lib").join("ffmpeg")
} else {
    // 默认路径
    base_path.join("lib").join("ffmpeg")
}
``` 