<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import { invoke } from "@tauri-apps/api/core";

  export let className = "";

  let containerRef: HTMLDivElement;
  let instanceCounter = 0;

  // 同步MPV窗口位置和大小
  async function handlePosSizeChange() {
    if (!containerRef) return;

    try {
      // 获取容器的位置和大小
      const {
        x: relX,
        y: relY,
        width,
        height,
      } = containerRef.getBoundingClientRect();

      // 调整位置和大小以适应边框和圆角
      // border-2 = 2px边框，rounded-lg圆角，添加一些额外边距
      const borderWidth = 2;
      const extraMargin = 4; // 额外边距，适应圆角和视觉效果
      const totalOffset = borderWidth + extraMargin;

      const adjustedX = relX + totalOffset;
      const adjustedY = relY + totalOffset;
      const adjustedWidth = width - totalOffset * 2;
      const adjustedHeight = height - totalOffset * 2;

      console.log(
        `MPV窗口位置同步: 原始(${relX}, ${relY}, ${width}, ${height}) -> 调整后(${adjustedX}, ${adjustedY}, ${adjustedWidth}, ${adjustedHeight})`
      );

      // 调用Tauri命令同步MPV窗口位置
      await invoke("sync_mpv_window_position", {
        x: adjustedX,
        y: adjustedY,
        width: adjustedWidth,
        height: adjustedHeight,
      });
    } catch (error) {
      console.error("同步MPV窗口位置失败:", error);
    }
  }

  onMount(() => {
    instanceCounter += 1;

    if (instanceCounter > 1) {
      console.warn("Cannot create multiple MPVWindowProxy instances");
      return;
    }

    if (!containerRef) return;

    // 监听各种事件
    const resizeObserver = new ResizeObserver(handlePosSizeChange);
    resizeObserver.observe(containerRef);

    window.addEventListener("resize", handlePosSizeChange);
    window.addEventListener("scroll", handlePosSizeChange);

    // 添加更多事件监听，确保位置同步
    document.addEventListener("scroll", handlePosSizeChange);
    containerRef.addEventListener("resize", handlePosSizeChange);

    // 初始同步
    handlePosSizeChange();

    // 延迟再次同步，确保初始位置正确
    setTimeout(handlePosSizeChange, 100);

    return () => {
      instanceCounter -= 1;
      resizeObserver.disconnect();
      window.removeEventListener("resize", handlePosSizeChange);
      window.removeEventListener("scroll", handlePosSizeChange);
      document.removeEventListener("scroll", handlePosSizeChange);
      containerRef?.removeEventListener("resize", handlePosSizeChange);
    };
  });

  onDestroy(() => {
    instanceCounter -= 1;
  });
</script>

<div bind:this={containerRef} class={className}>
  <slot />
</div>
