<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import { invoke } from "@tauri-apps/api/core";
  import { emit } from "@tauri-apps/api/event";
  import { Button } from "$lib/components/ui/button";
  import { Card, CardHeader, CardContent } from "$lib/components/ui/card";
  import { Input } from "$lib/components/ui/input";
  import { Label } from "$lib/components/ui/label";
  import { Slider } from "$lib/components/ui/slider";
  import MPVWindowProxy from "$lib/components/MPVWindowProxy.svelte";
  import {
    Play,
    Pause,
    RotateCcw,
    SkipForward,
    SkipBack,
    FastForward,
    Rewind,
    StepForward,
    StepBack,
    Volume2,
    VolumeX,
  } from "lucide-svelte";

  // 播放器状态
  let isPlaying = false;
  let currentTime = 0;
  let duration = 0;
  let speed = 1.0;
  let volume = 100;
  let lastVolume = 100; // 保存静音前的音量值
  let isMuted = false;
  let isVolumeChanging = false; // 标记是否正在进行音量操作
  let videoPath = "";
  let actionId = "";
  let showHelpModal = false; // 控制帮助窗口显示

  // 快捷键反馈相关
  let shortcutFeedback = "";
  let showFeedback = false;
  let feedbackTimer: number;

  // 时间标注相关
  let segments: Array<{
    id: string;
    start: number;
    end: number;
    startTime: string;
    endTime: string;
  }> = [];
  let startTimeInput = "00:00:00";
  let endTimeInput = "00:00:00";
  let editingSegmentId: string | null = null;

  // 定时器
  let updateTimer: number;

  // 清理MPV播放器
  async function cleanupMpv() {
    try {
      await invoke("cleanup_mpv_player");
    } catch (error) {
      console.error("清理MPV播放器失败:", error);
    }
  }

  onMount(async () => {
    try {
      // 从URL参数获取视频路径和action ID
      const urlParams = new URLSearchParams(window.location.search);
      const path = urlParams.get("path");
      const actionIdParam = urlParams.get("actionId");

      if (path) {
        videoPath = decodeURIComponent(path);

        await loadVideo(videoPath);
      } else {
        console.error("未获取到视频路径参数");
      }

      if (actionIdParam) {
        actionId = decodeURIComponent(actionIdParam);
      }

      // 启动定时器更新播放状态
      updateTimer = setInterval(updatePlayerStatus, 1000);

      // 监听页面卸载事件，确保MPV被清理
      window.addEventListener("beforeunload", cleanupMpv);
      window.addEventListener("unload", cleanupMpv);

      // 添加全局键盘事件监听
      window.addEventListener("keydown", handleGlobalKeyDown);
    } catch (error) {
      console.error("初始化失败:", error);
    }
  });

  onDestroy(async () => {
    if (updateTimer) {
      clearInterval(updateTimer);
    }
    window.removeEventListener("beforeunload", cleanupMpv);
    window.removeEventListener("unload", cleanupMpv);
    window.removeEventListener("keydown", handleGlobalKeyDown);

    try {
      await invoke("cleanup_mpv_player");
    } catch (error) {
      console.error("清理MPV播放器失败:", error);
    }
  });

  async function loadVideo(path: string) {
    try {
      await invoke("preview_mpv_load_file", { path });
      await updatePlayerStatus();
    } catch (error) {
      console.error("加载视频失败:", error);
    }
  }

  async function updatePlayerStatus() {
    try {
      const [paused, position, dur, spd, vol, muted] = await Promise.all([
        invoke("preview_mpv_is_paused"),
        invoke("preview_mpv_get_position"),
        invoke("preview_mpv_get_duration"),
        invoke("preview_mpv_get_speed"),
        invoke("preview_mpv_get_volume"),
        invoke("preview_mpv_get_mute"),
      ]);

      isPlaying = !(paused as boolean);
      currentTime = (position as number) || 0;
      duration = (dur as number) || 0;
      speed = (spd as number) || 1.0;

      // 如果正在进行音量操作，不要从MPV获取音量和静音状态
      if (!isVolumeChanging) {
        const currentVolume = Math.max(1, (vol as number) || 100);
        const currentMuted = muted as boolean;

        volume = currentVolume;
        isMuted = currentMuted;

        // 如果不是静音状态，更新lastVolume
        if (volume > 0 && !isMuted) {
          lastVolume = volume;
        }
      }
    } catch (error) {
      // 忽略错误，可能是播放器还未初始化
    }
  }

  async function setVolume(newVolume: number) {
    try {
      isVolumeChanging = true;

      // 确保音量不会设为0（最小为1）
      const safeVolume = Math.max(1, newVolume);

      await invoke("preview_mpv_set_volume", { volume: safeVolume });

      // 如果当前是静音状态，自动取消静音
      if (isMuted) {
        await invoke("preview_mpv_set_mute", { muted: false });
        isMuted = false;
      }

      // 保存当前音量作为lastVolume
      lastVolume = safeVolume;
      volume = safeVolume;

      // 延迟一下再允许状态更新，确保MPV已经处理完音量设置
      setTimeout(() => {
        isVolumeChanging = false;
      }, 500);
    } catch (error) {
      console.error("设置音量失败:", error);
      isVolumeChanging = false;
    }
  }

  async function toggleMute() {
    try {
      isVolumeChanging = true;

      // 直接切换MPV的静音状态
      await invoke("preview_mpv_set_mute", { muted: !isMuted });

      // 更新本地状态
      isMuted = !isMuted;

      // 如果取消静音，确保音量至少为1
      if (!isMuted && volume < 1) {
        const restoreVolume = Math.max(1, lastVolume);
        await invoke("preview_mpv_set_volume", { volume: restoreVolume });
        volume = restoreVolume;
        lastVolume = restoreVolume;
      }

      // 延迟一下再允许状态更新
      setTimeout(() => {
        isVolumeChanging = false;
      }, 500);
    } catch (error) {
      console.error("切换静音失败:", error);
      isVolumeChanging = false;
    }
  }

  async function seekToPosition(position: number) {
    try {
      await invoke("preview_mpv_seek", { position });
      await updatePlayerStatus();
    } catch (error) {
      console.error("跳转到指定位置失败:", error);
    }
  }

  async function togglePlayPause() {
    try {
      if (isPlaying) {
        await invoke("preview_mpv_pause");
      } else {
        await invoke("preview_mpv_play");
      }
      await updatePlayerStatus();
    } catch (error) {
      console.error("播放/暂停失败:", error);
    }
  }

  async function restartFromBeginning() {
    try {
      await invoke("preview_mpv_seek", { position: 0 });
      await invoke("preview_mpv_play");
      await updatePlayerStatus();
    } catch (error) {
      console.error("从头播放失败:", error);
    }
  }

  async function seek(seconds: number) {
    try {
      await invoke("preview_mpv_seek_relative", { offset: seconds });
      await updatePlayerStatus();
    } catch (error) {
      console.error("跳转失败:", error);
    }
  }

  async function setPlaybackSpeed(newSpeed: number) {
    try {
      await invoke("preview_mpv_set_speed", { speed: newSpeed });
      speed = newSpeed;
    } catch (error) {
      console.error("设置播放速度失败:", error);
    }
  }

  async function frameStep() {
    try {
      await invoke("preview_mpv_frame_step");
      await updatePlayerStatus();
    } catch (error) {
      console.error("帧步进失败:", error);
    }
  }

  async function frameBackStep() {
    try {
      await invoke("preview_mpv_frame_back_step");
      await updatePlayerStatus();
    } catch (error) {
      console.error("帧后退失败:", error);
    }
  }

  function formatTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }

  function parseTime(timeStr: string): number {
    const parts = timeStr.split(":");
    if (parts.length !== 3) return 0;
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseInt(parts[2]) || 0;
    return hours * 3600 + minutes * 60 + seconds;
  }

  function markStartTime() {
    startTimeInput = formatTime(currentTime);
  }

  function markEndTime() {
    endTimeInput = formatTime(currentTime);
  }

  function jumpToStartTime() {
    const time = parseTime(startTimeInput);
    seekToPosition(time);
  }

  function jumpToEndTime() {
    const time = parseTime(endTimeInput);
    seekToPosition(time);
  }

  function addSegment() {
    let start = parseTime(startTimeInput);
    let end = parseTime(endTimeInput);

    // 如果开始时间和结束时间都是默认值，使用当前播放时间创建一个5秒的片段
    if (start === 0 && end === 0) {
      start = currentTime;
      end = currentTime + 5; // 默认5秒片段
      startTimeInput = formatTime(start);
      endTimeInput = formatTime(end);
    }

    if (start >= end) {
      alert(
        "开始时间必须小于结束时间！\n请先使用Z键标注开始时间，X键标注结束时间，或手动输入时间。"
      );
      return;
    }

    const newSegment = {
      id: Date.now().toString(),
      start,
      end,
      startTime: startTimeInput,
      endTime: endTimeInput,
    };

    segments = [...segments, newSegment];

    // 清空输入
    startTimeInput = "00:00:00";
    endTimeInput = "00:00:00";
    editingSegmentId = null;

    console.log("片段添加成功:", newSegment);
  }

  function editSegment(segmentId: string) {
    const segment = segments.find((s) => s.id === segmentId);
    if (segment) {
      startTimeInput = segment.startTime;
      endTimeInput = segment.endTime;
      editingSegmentId = segmentId;
    }
  }

  function updateSegment() {
    if (editingSegmentId) {
      const start = parseTime(startTimeInput);
      const end = parseTime(endTimeInput);

      if (start >= end) {
        console.error("开始时间必须小于结束时间");
        return;
      }

      segments = segments.map((segment) =>
        segment.id === editingSegmentId
          ? {
              ...segment,
              start,
              end,
              startTime: startTimeInput,
              endTime: endTimeInput,
            }
          : segment
      );

      // 清空输入
      startTimeInput = "00:00:00";
      endTimeInput = "00:00:00";
      editingSegmentId = null;
    }
  }

  function deleteSegment(segmentId: string) {
    segments = segments.filter((s) => s.id !== segmentId);
    if (editingSegmentId === segmentId) {
      editingSegmentId = null;
      startTimeInput = "00:00:00";
      endTimeInput = "00:00:00";
    }
  }

  function cancelEdit() {
    editingSegmentId = null;
    startTimeInput = "00:00:00";
    endTimeInput = "00:00:00";
  }

  // 时间字符串转秒数（用于排序）
  function timeStringToSeconds(timeStr: string): number {
    const parts = timeStr.split(":");
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseInt(parts[2]) || 0;
    return hours * 3600 + minutes * 60 + seconds;
  }

  // 排序片段
  function sortSegments() {
    if (segments.length < 2) return;

    segments = segments.sort(
      (a, b) =>
        timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime)
    );
  }

  // 删除全部片段
  function clearAllSegments() {
    segments = [];
  }

  // 全局键盘事件处理
  function handleGlobalKeyDown(event: KeyboardEvent) {
    // 检查是否在输入框中，如果是则不处理快捷键
    const target = event.target as HTMLElement;
    if (
      target &&
      (target.tagName === "INPUT" ||
        target.tagName === "TEXTAREA" ||
        target.isContentEditable)
    ) {
      return;
    }

    // 防止默认行为
    const preventDefault = () => {
      event.preventDefault();
      event.stopPropagation();
    };

    switch (event.key) {
      // 播放速度控制 (1-5键)
      case "1":
        preventDefault();
        setPlaybackSpeed(0.5);
        showShortcutFeedback("播放速度: 0.5x");
        break;
      case "2":
        preventDefault();
        setPlaybackSpeed(1.0);
        showShortcutFeedback("播放速度: 1.0x");
        break;
      case "3":
        preventDefault();
        setPlaybackSpeed(1.5);
        showShortcutFeedback("播放速度: 1.5x");
        break;
      case "4":
        preventDefault();
        setPlaybackSpeed(2.0);
        showShortcutFeedback("播放速度: 2.0x");
        break;
      case "5":
        preventDefault();
        setPlaybackSpeed(4.0);
        showShortcutFeedback("播放速度: 4.0x");
        break;

      // 后退时间控制 (q,w,e,r,t)
      case "q":
        preventDefault();
        seek(-1);
        showShortcutFeedback("后退 1秒");
        break;
      case "w":
        preventDefault();
        seek(-5);
        showShortcutFeedback("后退 5秒");
        break;
      case "e":
        preventDefault();
        seek(-60);
        showShortcutFeedback("后退 1分钟");
        break;
      case "r":
        preventDefault();
        seek(-300); // 5分钟
        showShortcutFeedback("后退 5分钟");
        break;
      case "t":
        preventDefault();
        seek(-600); // 10分钟
        showShortcutFeedback("后退 10分钟");
        break;

      // 快进时间控制 (a,s,d,f,g)
      case "a":
        preventDefault();
        seek(1);
        showShortcutFeedback("快进 1秒");
        break;
      case "s":
        preventDefault();
        seek(5);
        showShortcutFeedback("快进 5秒");
        break;
      case "d":
        preventDefault();
        seek(60);
        showShortcutFeedback("快进 1分钟");
        break;
      case "f":
        preventDefault();
        seek(300); // 5分钟
        showShortcutFeedback("快进 5分钟");
        break;
      case "g":
        preventDefault();
        seek(600); // 10分钟
        showShortcutFeedback("快进 10分钟");
        break;

      // 方向键控制
      case "ArrowUp":
        preventDefault();
        seek(1); // 前进1秒
        showShortcutFeedback("前进 1秒");
        break;
      case "ArrowDown":
        preventDefault();
        seek(-1); // 后退1秒
        showShortcutFeedback("后退 1秒");
        break;
      case "ArrowLeft":
        preventDefault();
        frameBackStep(); // 后退一帧
        showShortcutFeedback("后退一帧");
        break;
      case "ArrowRight":
        preventDefault();
        frameStep(); // 前进一帧
        showShortcutFeedback("前进一帧");
        break;

      // 标注功能 (z,x,c)
      case "z":
        preventDefault();
        markStartTime();
        showShortcutFeedback(`开始时间: ${formatTime(currentTime)}`);
        break;
      case "x":
        preventDefault();
        markEndTime();
        showShortcutFeedback(`结束时间: ${formatTime(currentTime)}`);
        break;
      case "c":
        preventDefault();
        addSegment();
        showShortcutFeedback("添加片段");
        break;

      // 其他控制
      case "0":
        preventDefault();
        seekToPosition(0); // 跳转到开头
        showShortcutFeedback("跳转到开头");
        break;
      case "9":
        preventDefault();
        if (duration > 0) {
          seekToPosition(duration - 0.1); // 跳转到结尾，稍微退后一点
          showShortcutFeedback("跳转到结尾");
        }
        break;
      case " ":
        preventDefault();
        togglePlayPause(); // 播放/暂停
        showShortcutFeedback(isPlaying ? "暂停" : "播放");
        break;
      case "m":
        preventDefault();
        toggleMute(); // 静音开关
        showShortcutFeedback(isMuted ? "取消静音" : "静音");
        break;
      case "o":
        preventDefault();
        restartFromBeginning(); // 重头播放
        showShortcutFeedback("重头播放");
        break;
      case "-":
        preventDefault();
        // 音量减
        if (volume > 1) {
          const newVolume = Math.max(1, volume - 5);
          setVolume(newVolume);
          showShortcutFeedback(`音量: ${newVolume}%`);
        }
        break;
      case "=":
      case "+":
        preventDefault();
        // 音量加
        if (volume < 100) {
          const newVolume = Math.min(100, volume + 5);
          setVolume(newVolume);
          showShortcutFeedback(`音量: ${newVolume}%`);
        }
        break;
      case "h":
        preventDefault();
        showHelpDialog();
        showShortcutFeedback(showHelpModal ? "隐藏帮助" : "显示帮助");
        break;
    }
  }

  // 显示/隐藏快捷键帮助对话框
  function showHelpDialog() {
    showHelpModal = !showHelpModal;
  }

  // 显示快捷键操作反馈
  function showShortcutFeedback(message: string) {
    shortcutFeedback = message;
    showFeedback = true;

    // 清除之前的定时器
    if (feedbackTimer) {
      clearTimeout(feedbackTimer);
    }

    // 2秒后自动隐藏反馈
    feedbackTimer = setTimeout(() => {
      showFeedback = false;
    }, 2000);
  }

  // 关闭帮助对话框
  function closeHelpDialog() {
    showHelpModal = false;
  }

  async function exportSegments() {
    if (segments.length === 0) {
      console.error("请先添加至少一个时间片段");
      return;
    }

    try {
      // 导出前先排序
      const sortedSegments = [...segments].sort(
        (a, b) =>
          timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime)
      );

      // 将时间段数据传递给主窗口
      const segmentData = sortedSegments.map((segment) => ({
        startTime: segment.start, // 数字类型（秒数）
        endTime: segment.end, // 数字类型（秒数）
        startTimeStr: segment.startTime, // 字符串类型（HH:MM:SS）
        endTimeStr: segment.endTime, // 字符串类型（HH:MM:SS）
      }));

      // 直接通过前端事件系统发送数据到主窗口
      await emit("segments_exported", {
        segments: segmentData,
        videoPath: videoPath,
        actionId: actionId, // 传递action ID
      });
    } catch (error) {
      console.error("导出失败:", error);
    }
  }
</script>

<div class="container mx-auto p-4 h-screen flex flex-col">
  <Card class="flex-1 flex flex-col">
    <CardHeader class="pb-2">
      <h2 class="text-xl font-bold">视频预览 - {videoPath}</h2>
    </CardHeader>
    <CardContent class="flex-1 flex flex-col space-y-4">
      <!-- 顶部时间和速度控制 -->
      <div class="flex items-center justify-between">
        <div class="text-lg font-mono">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>

        <!-- 快捷键操作反馈显示区域 -->
        {#if showFeedback}
          <div
            class="bg-blue-100 border border-blue-300 rounded-lg px-3 py-1 text-sm font-medium text-blue-800 animate-pulse"
          >
            {shortcutFeedback}
          </div>
        {/if}

        <div class="flex items-center space-x-2">
          <span class="text-sm">播放速度:</span>
          <Button
            variant="outline"
            size="sm"
            onclick={() => setPlaybackSpeed(0.5)}>0.5x</Button
          >
          <Button
            variant="outline"
            size="sm"
            onclick={() => setPlaybackSpeed(1.0)}>1.0x</Button
          >
          <Button
            variant="outline"
            size="sm"
            onclick={() => setPlaybackSpeed(1.5)}>1.5x</Button
          >
          <Button
            variant="outline"
            size="sm"
            onclick={() => setPlaybackSpeed(2.0)}>2.0x</Button
          >
          <Button
            variant="outline"
            size="sm"
            onclick={() => setPlaybackSpeed(4.0)}>4.0x</Button
          >
          <span class="text-sm">当前: {speed}x</span>
        </div>
      </div>

      <!-- 视频显示区域 (MPV窗口将同步到此位置) -->
      <div class="flex-1">
        <MPVWindowProxy
          className="w-full h-full bg-black border-2 border-gray-300 rounded-lg flex items-center justify-center"
        >
          <div class="text-white text-center">
            <div class="text-lg font-medium">MPV 播放器</div>
            <div class="text-sm text-gray-300 mt-2">视频将在此区域显示</div>
          </div>
        </MPVWindowProxy>
      </div>

      <!-- 快捷键帮助区域 - 显示在视频下方 -->
      {#if showHelpModal}
        <div class="bg-white border rounded-lg p-4 max-h-48 overflow-y-auto">
          <div class="flex justify-between items-center mb-3">
            <h3 class="text-lg font-bold">快捷键说明</h3>
            <Button variant="outline" size="sm" onclick={closeHelpDialog}
              >关闭</Button
            >
          </div>

          <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-xs">
            <div>
              <h4 class="font-semibold mb-1 text-blue-600">播放速度</h4>
              <div class="space-y-0.5">
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >1</span
                  ><span>0.5x</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >2</span
                  ><span>1x</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >3</span
                  ><span>1.5x</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >4</span
                  ><span>2x</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >5</span
                  ><span>4x</span>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-semibold mb-1 text-green-600">后退</h4>
              <div class="space-y-0.5">
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >Q</span
                  ><span>1秒</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >W</span
                  ><span>5秒</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >E</span
                  ><span>1分</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >R</span
                  ><span>5分</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >T</span
                  ><span>10分</span>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-semibold mb-1 text-green-600">快进</h4>
              <div class="space-y-0.5">
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >A</span
                  ><span>1秒</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >S</span
                  ><span>5秒</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >D</span
                  ><span>1分</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >F</span
                  ><span>5分</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >G</span
                  ><span>10分</span>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-semibold mb-1 text-purple-600">方向键</h4>
              <div class="space-y-0.5">
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >↑</span
                  ><span>进1秒</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >↓</span
                  ><span>退1秒</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >←</span
                  ><span>退1帧</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >→</span
                  ><span>进1帧</span>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-semibold mb-1 text-orange-600">标注</h4>
              <div class="space-y-0.5">
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >Z</span
                  ><span>开始</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >X</span
                  ><span>结束</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >C</span
                  ><span>添加</span>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-semibold mb-1 text-red-600">其他</h4>
              <div class="space-y-0.5">
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >0</span
                  ><span>开头</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >9</span
                  ><span>结尾</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >空格</span
                  ><span>播放</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >M</span
                  ><span>静音</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono bg-gray-100 px-1 rounded text-xs"
                    >-/=</span
                  ><span>音量</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      {/if}

      <!-- 进度条 -->
      <div class="w-full px-2">
        <Slider
          value={currentTime}
          min={0}
          max={duration || 100}
          step={0.1}
          class="mb-2"
          onchange={(value) => seekToPosition(value)}
        />
      </div>

      <!-- 播放控制区域 -->
      <div class="flex items-center justify-between">
        <!-- 左侧播放控制按钮 -->
        <div class="flex items-center space-x-2">
          <Button variant="outline" size="sm" onclick={() => seek(-60)}>
            <Rewind class="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onclick={() => seek(-5)}>
            <SkipBack class="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onclick={frameBackStep}>
            <StepBack class="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onclick={togglePlayPause}>
            {#if isPlaying}
              <Pause class="w-4 h-4" />
            {:else}
              <Play class="w-4 h-4" />
            {/if}
          </Button>
          <Button variant="outline" size="sm" onclick={restartFromBeginning}>
            <RotateCcw class="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onclick={frameStep}>
            <StepForward class="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onclick={() => seek(5)}>
            <SkipForward class="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onclick={() => seek(60)}>
            <FastForward class="w-4 h-4" />
          </Button>
        </div>

        <!-- 右侧音量控制 -->
        <div class="flex items-center space-x-2">
          <Button variant="outline" size="sm" onclick={toggleMute}>
            {#if isMuted}
              <VolumeX class="w-4 h-4" />
            {:else}
              <Volume2 class="w-4 h-4" />
            {/if}
          </Button>
          <Slider
            value={volume}
            min={1}
            max={100}
            step={1}
            class="w-24"
            onchange={(value) => setVolume(value)}
          />
        </div>
      </div>

      <!-- 时间标注区域 -->
      <div class="space-y-3">
        <h3 class="text-lg font-semibold">
          时间标注 <span class="text-sm text-gray-500 font-normal"
            >(按H显示快捷键帮助)</span
          >
        </h3>

        <!-- 时间输入区域 -->
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label>开始时间</Label>
            <div class="flex space-x-2">
              <Input bind:value={startTimeInput} placeholder="00:00:00" />
              <Button variant="outline" size="sm" onclick={markStartTime}
                >标注</Button
              >
              <Button variant="outline" size="sm" onclick={jumpToStartTime}
                >跳转</Button
              >
            </div>
          </div>
          <div class="space-y-2">
            <Label>结束时间</Label>
            <div class="flex space-x-2">
              <Input bind:value={endTimeInput} placeholder="00:00:00" />
              <Button variant="outline" size="sm" onclick={markEndTime}
                >标注</Button
              >
              <Button variant="outline" size="sm" onclick={jumpToEndTime}
                >跳转</Button
              >
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-center space-x-2">
          {#if editingSegmentId}
            <Button onclick={updateSegment}>更新片段</Button>
            <Button variant="outline" onclick={cancelEdit}>取消</Button>
          {:else}
            <Button onclick={addSegment}>添加片段</Button>
          {/if}
          <Button variant="outline" onclick={exportSegments}>导出</Button>
          <Button variant="outline" onclick={sortSegments}>排序</Button>
          <Button variant="destructive" onclick={clearAllSegments}
            >删除全部</Button
          >
        </div>

        <!-- 已添加的片段 - 可滚动区域 -->
        {#if segments.length > 0}
          <div class="space-y-2">
            <h4 class="font-medium">已添加的片段:</h4>
            <div class="max-h-32 overflow-y-auto space-y-2 border rounded p-2">
              {#each segments as segment}
                <div
                  class="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
                >
                  <span class="font-mono">
                    {segment.startTime} - {segment.endTime}
                  </span>
                  <div class="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onclick={() => editSegment(segment.id)}>编辑</Button
                    >
                    <Button
                      variant="destructive"
                      size="sm"
                      onclick={() => deleteSegment(segment.id)}>删除</Button
                    >
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    </CardContent>
  </Card>
</div>
