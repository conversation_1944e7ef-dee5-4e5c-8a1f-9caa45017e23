VideoIDE 设计文档

该文档描述一些复杂功能的具体设计

## 设计一个标准视频播放的组件
### 说明
这个组件的主要目的是可以预览视频, 并且可以标注视频的开始和结束时间, 用于一些需要选择视频片段的动作中, 比如截取片段, 提取音频等
在主界面某些动作(比如`剪辑`)提供按钮点击后打开这个窗口, 进行标注若干片段, 完成后可以加载到主界面动作的参数列表里, 用来剪辑合并所标注的所有片段

### 需求
1. 主窗口点击一个按钮(预览)打开一个新窗口, 该窗口加载已选择的视频
2. 提供一组基本播放按钮, 包括开始, 跳5秒, 跳1分种, 加速播放, 减速播放, 步进(按帧跳), 停止等
3. 下方提供一个标注开始按钮, 以及后面文本框(记录标注的开始时间), 后面有一个调整按钮, 点击后视频跳到文本框指定时间
4. 同样, 下方提供一个标注结束按钮, 以及后面文本框(记录标注的结束时间), 后面有一个调整按钮, 点击后视频跳到文本框指定时间
5. 可以多次操作, 因为有的动作需要有多个片段选择后再连接起来
6. 标注完成后, 点击确定按钮, 关闭窗口, 并将标注的开始和结束时间加载到主界面动作的参数列表里, 用来剪辑合并所标注的所有片段

### 技术要求
1. 使用 `mpv` 的 `libmpv-2.dll` 和 FFI 技术播放本地视频文件
2. 由于不能在webview里直接嵌入mpv , 使用一种叠加的技术, 就是单独使用mpv启动播放文件, 并将窗口
放置在webview 窗口的指定位置, 造成一种视觉上的嵌入, 然后在webview 上提供一排播放控制按钮, 用来控制视频播放
3. 我已经找到一个类似此需求的项目,在`media_player`目录下, 请参考, 里面有一个`project_intro.md` 也有项目的一些技术细节
4. 实现时要使用我的项目技术栈 `Tauri v2 + svelte 5 + rust` , 和`media_player`项目不完全一致
5. 我已经下载好 `libmpv-2.dll` 文件放在 `src-tauri/lib/mpv` 下

### 快捷操作
由于用户键视频需要来回反复观看视频, 所以有一套便捷的快捷键操作很重要, 这套快捷键是预览窗口全局的, 注意播放控制需要和左上方时间同步
- 1,2,3,4,5 - 映射到 0.5, 1, 1.5, 2, 4倍速
- q,w,e,r,t - 映射到后退1秒, 5秒, 1分, 5分, 10分
- a,s,d,f,g - 映射到快进1秒, 5秒, 1分, 5分, 10分
- 方向键上下左右分别对应 - 前进1秒, 后退1秒, 后退一帧, 前进一帧
- z为标注开始, x为标注结束, c为添加
- 0到视频开头, 9为到视频末尾
- 空格(暂停,播放)
- m(静音开关)
- o(重头播放)
- -(音量减), =(音量加)
- h(弹出一个窗口显示所有快捷键定义说明)
要注意当在下方时间输入框时, 要捕获键盘事件, 不要发送到全局控制视频

### 控制栏区域右边增加进度条控制
将原播放控制按钮栏移到左边, 右边增加一个进度条以及音量控制(静音和音量拖拉调节), 用户
可以方便的拖拉定位, 见图片,注意进度条控制需要和左上方时间同步


## 关于视频动作链管理的设计
视频动作链就是将当前组织好的动作按顺序保存起来, 方便下次复用, 包括所有的参数设置.
比如当前添加 `截掉头部30秒` -> `添加封面图片` -> `加上背景音乐`, 将这个动作链保存后, 包括全部的
参数设置, 下次可以直接下载, 选择一个新视频, 点击`开始处理` 就能快速应用这套动作链, 当然用户也可以
调整部分参数后再开始处理.

### 基本功能
- `动作链` 保存, 删除, 列表, 重命名
- `动作链` 导出, 导入, 格式都是标准json文件, 包含`动作链`的所有信息`动作id` 如`crop`等, 参数, 在列表中顺序和名称, 导入功能需要完全恢复`动作链`的配置
- 默认保存位置 - mac和windows都用各自标准的 appdata 目录, 新建一个`videoide` 目录, 新建`action`目录
```
macOS
~/Library/Application Support/<AppName>

Windows
C:\users\<USER>\Application Data\<AppName>
```

- 如果导入别的位置`动作链`json文件时, 应先校验格式, 确保符合要求
- 导入动作链时如果有同名, 自动将名称后面加个数字`2`
- 加载`动作链`时如某些参数, 如文件路径需要判断文件是否存在, 不存在则设置为空, 方便重新设置

### UI 设计
可以放在主界面和`处理列表`, `动作链管理` 并排成为两个tab, 方便切换调用

`动作链` 有常规的列表, 每行有`加载`, `重命名`, `删除`按钮, `导出`按钮
 最上面有统计 `5个动作链` , `导入`按钮 

`处理列表tab` 上方显示当前`动作链`名, 或`未保存`如果是新得动作链, 有 `保存` 按钮, `另存为` - 相当于复制
同时`处理列表tab`也可以对当前动作链做一些`保存`等操作, `处理列表tab`可以`新建`动作链, 清除掉当前动作链加载


## 优化主窗口布局, 最大化用户可用空间
主要目的: 增加用户可用空间, 减少视觉干扰(很多边框等), 使用颜色区分边界, 使ui看起来更简洁, 更有整体感 
- 整体布局为左右布局, 右边整体上移, 最大化使用空间
- 减少边框使用, 左右两栏不以边框区分, 以背景色区分, 左边使用浅灰色, 右边白色, 类似devtoys
- 左边栏下部分固定, 和上面动作列表分开, 下部放置 切换明暗风格, 账户, 设置等预留按钮(只有一行, 按钮横向排列居中)
- 缩小主标题字体
- 减少动作列表于搜索按钮间隙
- 移除`可用动作`标题, 保留`点击动作添加到处理列表`, 精简内容, 增大实际使用空间 
- 增加键盘快捷键
    F3-定位到搜索栏, 这样用户可以快捷搜索, 然后按tab选择动作并按enter键添加到处理列表, 优化处理流程
    F1-打开文件选择对话框, 可以快捷先择文件
    F2-打开文件夹选择对话框, 可以快捷先择文件夹
    Ctrl+1, Ctrl+2 切换tab
    以上在相应地方添加快捷键提示, 比如`选择文件(F1)`, `搜索动作(F3)`