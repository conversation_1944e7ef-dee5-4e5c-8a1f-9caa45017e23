{"$schema": "https://schema.tauri.app/config/2", "productName": "VideoIDE", "version": "0.2.0", "identifier": "com.videoide.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../build"}, "app": {"windows": [{"title": "VideoIDE", "width": 1280, "height": 800, "resizable": true, "fullscreen": false, "dragDropEnabled": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["lib/**/*"]}}