// 验证工具函数
import type {
  ActionParams,
  ActionParamDefinition,
  ValidationResult,
} from "../types/action";

/**
 * 验证单个参数
 * @param value 参数值
 * @param definition 参数定义
 * @returns 验证结果
 */
export function validateParam(
  value: any,
  definition: ActionParamDefinition
): string[] {
  const errors: string[] = [];

  // 检查必需参数
  if (
    definition.required &&
    (value === undefined || value === null || value === "")
  ) {
    errors.push(`Parameter ${definition.key} is required`);
    return errors;
  }

  // 如果值为空且不是必需的，跳过验证
  if (value === undefined || value === null || value === "") {
    return errors;
  }

  // 类型验证
  switch (definition.type) {
    case "number":
    case "duration":
      // 对于 duration 类型，允许字符串输入并尝试转换为数字
      const numValue =
        definition.type === "duration"
          ? parseFloat(value)
          : typeof value === "number"
          ? value
          : NaN;
      if (isNaN(numValue)) {
        errors.push(`Parameter ${definition.key} must be a valid number`);
      } else {
        if (definition.min !== undefined && numValue < definition.min) {
          errors.push(
            `Parameter ${definition.key} must be >= ${definition.min}`
          );
        }
        if (definition.max !== undefined && numValue > definition.max) {
          errors.push(
            `Parameter ${definition.key} must be <= ${definition.max}`
          );
        }
      }
      break;

    case "range":
      const rangeValue = Number(value);
      if (isNaN(rangeValue)) {
        errors.push(`Parameter ${definition.key} must be a valid number`);
      } else {
        if (definition.min !== undefined && rangeValue < definition.min) {
          errors.push(
            `Parameter ${definition.key} must be >= ${definition.min}`
          );
        }
        if (definition.max !== undefined && rangeValue > definition.max) {
          errors.push(
            `Parameter ${definition.key} must be <= ${definition.max}`
          );
        }
      }
      break;

    case "string":
      if (typeof value !== "string") {
        errors.push(`Parameter ${definition.key} must be a string`);
      }
      break;

    case "boolean":
      if (typeof value !== "boolean") {
        errors.push(`Parameter ${definition.key} must be a boolean`);
      }
      break;

    case "file":
      if (typeof value !== "string" || value.trim() === "") {
        errors.push(`Parameter ${definition.key} must be a valid file path`);
      }
      break;

    case "select":
      if (definition.options) {
        const validValues = definition.options.map((opt) => opt.value);
        if (!validValues.includes(value)) {
          errors.push(
            `Parameter ${definition.key} must be one of: ${validValues.join(
              ", "
            )}`
          );
        }
      }
      break;

    case "color":
      if (typeof value !== "string" || !/^#[0-9A-Fa-f]{6}$/.test(value)) {
        errors.push(`Parameter ${definition.key} must be a valid hex color`);
      }
      break;

    case "resolution":
      if (typeof value !== "object" || !value.width || !value.height) {
        errors.push(
          `Parameter ${definition.key} must be a valid resolution object`
        );
      } else {
        if (value.width <= 0 || value.height <= 0) {
          errors.push(
            `Parameter ${definition.key} resolution must be positive`
          );
        }
      }
      break;

    case "position":
      if (
        typeof value !== "object" ||
        value.x === undefined ||
        value.y === undefined
      ) {
        errors.push(
          `Parameter ${definition.key} must be a valid position object`
        );
      } else {
        if (value.x < 0 || value.y < 0) {
          errors.push(
            `Parameter ${definition.key} position must be non-negative`
          );
        }
      }
      break;
  }

  // 自定义验证
  if (definition.validation) {
    const result = definition.validation(value);
    if (typeof result === "string") {
      errors.push(result);
    } else if (result === false) {
      errors.push(`Parameter ${definition.key} validation failed`);
    }
  }

  return errors;
}

/**
 * 验证所有参数
 * @param params 参数值对象
 * @param definitions 参数定义数组
 * @returns 验证结果
 */
export function validateAllParams(
  params: ActionParams,
  definitions: ActionParamDefinition[]
): ValidationResult {
  const allErrors: string[] = [];

  for (const definition of definitions) {
    const value = params[definition.key];
    const errors = validateParam(value, definition);
    allErrors.push(...errors);
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
  };
}

/**
 * 获取参数的默认值
 * @param definition 参数定义
 * @returns 默认值
 */
export function getParamDefaultValue(definition: ActionParamDefinition): any {
  if (definition.defaultValue !== undefined) {
    return definition.defaultValue;
  }

  switch (definition.type) {
    case "number":
    case "range":
    case "duration":
      return definition.min ?? 0;
    case "string":
    case "file":
    case "color":
      return "";
    case "boolean":
      return false;
    case "select":
      return definition.options?.[0]?.value ?? "";
    case "resolution":
      return { width: 1920, height: 1080 };
    case "position":
      return { x: 0, y: 0 };
    default:
      return null;
  }
}

/**
 * 创建参数的默认值对象
 * @param definitions 参数定义数组
 * @returns 默认参数对象
 */
export function createDefaultParamsObject(
  definitions: ActionParamDefinition[]
): ActionParams {
  const params: ActionParams = {};

  for (const definition of definitions) {
    params[definition.key] = getParamDefaultValue(definition);
  }

  return params;
}

/**
 * 清理参数值（移除undefined和null）
 * @param params 参数对象
 * @returns 清理后的参数对象
 */
export function cleanParams(params: ActionParams): ActionParams {
  const cleaned: ActionParams = {};

  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined && value !== null) {
      cleaned[key] = value;
    }
  }

  return cleaned;
}

/**
 * 合并参数（用新参数覆盖默认参数）
 * @param defaultParams 默认参数
 * @param userParams 用户参数
 * @returns 合并后的参数
 */
export function mergeParams(
  defaultParams: ActionParams,
  userParams: ActionParams
): ActionParams {
  return { ...defaultParams, ...cleanParams(userParams) };
}
