import { invoke } from "@tauri-apps/api/core";
import type { ActionChain, ActionChainInfo, ActionData } from "$lib/types/action-chain";

// 初始化动作链管理器
export async function initActionChainManager(): Promise<void> {
  return invoke("init_action_chain_manager");
}

// 保存动作链
export async function saveActionChain(
  name: string,
  description: string,
  actions: ActionData[]
): Promise<void> {
  return invoke("save_action_chain", {
    name,
    description,
    actions,
  });
}

// 加载动作链
export async function loadActionChain(name: string): Promise<ActionChain> {
  return invoke("load_action_chain", { name });
}

// 删除动作链
export async function deleteActionChain(name: string): Promise<void> {
  return invoke("delete_action_chain", { name });
}

// 重命名动作链
export async function renameActionChain(
  oldName: string,
  newName: string
): Promise<void> {
  return invoke("rename_action_chain", {
    oldName,
    newName,
  });
}

// 获取动作链列表
export async function listActionChains(): Promise<ActionChainInfo[]> {
  return invoke("list_action_chains");
}

// 更新使用统计
export async function updateActionChainUsage(name: string): Promise<void> {
  return invoke("update_action_chain_usage", { name });
}

// 导出动作链
export async function exportActionChain(
  name: string,
  exportPath: string
): Promise<void> {
  return invoke("export_action_chain", {
    name,
    exportPath,
  });
}

// 导入动作链
export async function importActionChain(importPath: string): Promise<string> {
  return invoke("import_action_chain", { importPath });
}

// 更新动作链描述
export async function updateActionChainDescription(
  name: string,
  description: string
): Promise<void> {
  return invoke("update_action_chain_description", {
    name,
    description,
  });
}
