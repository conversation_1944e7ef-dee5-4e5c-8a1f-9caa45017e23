// 动作系统初始化

import { registry } from "./registry";
import { categories } from "./categories";
import {
  trimActions,
  colorActions,
  audioActions,
  transformActions,
  effectActions,
  watermarkActions,
  encodeActions,
  imageActions,
  filterActions,
} from "./categories/index";

/**
 * 初始化动作系统
 * 注册所有类别和动作
 */
export function initializeActionSystem(): void {
  // 注册所有类别
  categories.forEach((category) => {
    registry.registerCategory(category);
  });

  // 注册所有动作
  const allActions = [
    ...trimActions,
    ...colorActions,
    ...audioActions,
    ...transformActions,
    ...effectActions,
    ...watermarkActions,
    ...encodeActions,
    ...imageActions,
    ...filterActions,
  ];

  allActions.forEach((action) => {
    registry.registerAction(action);
  });

  console.log(
    "Action system initialized with",
    registry.getAllCategories().length,
    "categories and",
    registry.getAllActions().length,
    "actions"
  );
}

/**
 * 获取动作系统统计信息
 */
export function getActionSystemStats() {
  return {
    categoriesCount: registry.getAllCategories().length,
    actionsCount: registry.getAllActions().length,
    actionsByCategory: registry.getAllCategories().map((category) => ({
      categoryId: category.id,
      categoryName: category.nameKey,
      actionsCount: registry.getActionsByCategory(category.id).length,
    })),
  };
}
