<script lang="ts">
  import { open } from "@tauri-apps/plugin-dialog";
  import { basename } from "@tauri-apps/api/path";
  import { invoke } from "@tauri-apps/api/core";
  import { Card, CardHeader, CardContent } from "$lib/components/ui/card";
  import { onMount } from "svelte";
  import { listen } from "@tauri-apps/api/event";
  import { getCurrentWindow } from "@tauri-apps/api/window";
  import { createEventDispatcher } from "svelte";

  interface VideoInfo {
    filename: string;
    duration: number;
    format: string;
    size_mb: number;
    video_codec: string;
    resolution: string;
    fps: string;
    video_bitrate: string;
    audio_codec: string;
    audio_sample_rate: string;
    audio_channels: number;
  }

  interface AudioInfo {
    filename: string;
    duration: number;
    format: string;
    size_mb: number;
    audio_codec: string;
    audio_sample_rate: string;
    audio_channels: number;
    audio_bitrate: string;
  }

  interface ImageInfo {
    filename: string;
    format: string;
    size_mb: number;
    resolution: string;
    color_space: string;
  }

  type FileInfo =
    | ({ type: "Video" } & VideoInfo)
    | ({ type: "Audio" } & AudioInfo)
    | ({ type: "Image" } & ImageInfo);

  interface Props {
    externalFilePath?: string | null;
    externalFileInfo?: FileInfo | null;
    onFileChange?: (filePath: string | null, fileInfo: FileInfo | null) => void;
  }

  let {
    externalFilePath = null,
    externalFileInfo = null,
    onFileChange,
  }: Props = $props();

  // 使用外部状态或内部状态
  let filePath: string | null = $state(externalFilePath);
  let fileInfo: FileInfo | null = $state(externalFileInfo);

  // 监听外部状态变化
  $effect(() => {
    if (externalFilePath !== undefined) {
      filePath = externalFilePath;
    }
  });

  $effect(() => {
    if (externalFileInfo !== undefined) {
      fileInfo = externalFileInfo;
    }
  });
  let isProcessing = $state(false);
  let isDragOver = $state(false);
  let isFolder = $state(false);
  let imageFiles: string[] = $state([]);

  const SUPPORTED_VIDEO_EXTS = [
    "mp4",
    "mov",
    "avi",
    "mkv",
    "wmv",
    "flv",
    "webm",
    "m4v",
  ];
  const SUPPORTED_AUDIO_EXTS = [
    "mp3",
    "wav",
    "aac",
    "flac",
    "m4a",
    "ogg",
    "wma",
  ];
  const SUPPORTED_IMAGE_EXTS = [
    "jpg",
    "jpeg",
    "png",
    "bmp",
    "gif",
    "webp",
    "tiff",
    "tga",
  ];
  const SUPPORTED_EXTS = [
    ...SUPPORTED_VIDEO_EXTS,
    ...SUPPORTED_AUDIO_EXTS,
    ...SUPPORTED_IMAGE_EXTS,
  ];

  const dispatch = createEventDispatcher();

  // 获取文件类型
  function getFileType(filePath: string): "video" | "audio" | "image" {
    const ext = filePath.toLowerCase().split(".").pop();
    if (!ext) return "video";

    if (SUPPORTED_VIDEO_EXTS.includes(ext)) return "video";
    if (SUPPORTED_AUDIO_EXTS.includes(ext)) return "audio";
    if (SUPPORTED_IMAGE_EXTS.includes(ext)) return "image";

    return "video"; // 默认
  }

  // 格式化时长显示
  function formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }

  async function handleFile(path: string) {
    filePath = path;
    const name = await basename(path);

    try {
      const info = await invoke<FileInfo>("get_file_info", {
        filePath: path,
      });
      fileInfo = info;
    } catch (e) {
      console.error("获取文件信息失败:", e);
      // 如果获取失败，创建一个基本的文件信息
      const fileType = getFileType(path);
      const format = path.split(".").pop()?.toUpperCase() || "N/A";
      if (fileType === "video") {
        fileInfo = {
          type: "Video",
          filename: name,
          duration: 0,
          format,
          size_mb: 0,
          video_codec: "未知",
          resolution: "未知",
          fps: "未知",
          video_bitrate: "未知",
          audio_codec: "未知",
          audio_sample_rate: "未知",
          audio_channels: 0,
        };
      } else if (fileType === "audio") {
        fileInfo = {
          type: "Audio",
          filename: name,
          duration: 0,
          format,
          size_mb: 0,
          audio_codec: "未知",
          audio_sample_rate: "未知",
          audio_channels: 0,
          audio_bitrate: "未知",
        };
      } else {
        fileInfo = {
          type: "Image",
          filename: name,
          format,
          size_mb: 0,
          resolution: "未知",
          color_space: "未知",
        };
      }
    }
    dispatch("fileChange");
    // 通知外部状态变化
    if (onFileChange) {
      onFileChange(filePath, fileInfo);
    }
  }

  async function selectFile() {
    const result = await open({
      multiple: false,
      filters: [
        { name: "所有支持的文件", extensions: SUPPORTED_EXTS },
        { name: "视频文件", extensions: SUPPORTED_VIDEO_EXTS },
        { name: "音频文件", extensions: SUPPORTED_AUDIO_EXTS },
        { name: "图片文件", extensions: SUPPORTED_IMAGE_EXTS },
      ],
    });

    if (typeof result === "string") {
      await handleFile(result);
    }
  }

  async function selectFolder() {
    const result = await open({
      multiple: false,
      directory: true,
    });

    if (typeof result === "string") {
      await handleFolder(result);
    }
  }

  async function handleFolder(folderPath: string) {
    filePath = folderPath;
    isFolder = true;
    fileInfo = null;

    try {
      // 获取文件夹信息
      const folderInfo = await invoke("get_folder_info", { folderPath });
      const info = folderInfo as {
        files: string[];
        total_size_mb: number;
        folder_type: string;
        file_counts: {
          image_count: number;
          video_count: number;
          audio_count: number;
          other_count: number;
        };
      };

      imageFiles = info.files;

      // 创建文件夹信息显示
      const folderName = folderPath.split(/[/\\]/).pop() || "未知文件夹";

      // 根据文件夹类型显示不同的信息
      let folderTypeText = "文件夹";
      let fileCountText = "";

      switch (info.folder_type) {
        case "image":
          folderTypeText = "图片文件夹";
          fileCountText = `${info.file_counts.image_count} 个图片文件`;
          break;
        case "video":
          folderTypeText = "视频文件夹";
          fileCountText = `${info.file_counts.video_count} 个视频文件`;
          break;
        case "audio":
          folderTypeText = "音频文件夹";
          fileCountText = `${info.file_counts.audio_count} 个音频文件`;
          break;
        case "mixed":
          folderTypeText = "混合文件夹";
          fileCountText = `${info.file_counts.image_count}图片 ${info.file_counts.video_count}视频 ${info.file_counts.audio_count}音频`;
          break;
        case "empty":
          folderTypeText = "空文件夹";
          fileCountText = "无媒体文件";
          break;
        default:
          folderTypeText = "文件夹";
          fileCountText = `${info.files.length} 个文件`;
      }

      fileInfo = {
        type: "Image",
        filename: folderName,
        format: "folder",
        size_mb: info.total_size_mb,
        resolution: fileCountText,
        color_space: folderTypeText,
      } as FileInfo;
    } catch (error) {
      console.error("获取文件夹信息失败:", error);
      imageFiles = [];

      // 创建错误信息显示
      const folderName = folderPath.split(/[/\\]/).pop() || "未知文件夹";
      fileInfo = {
        type: "Image",
        filename: folderName,
        format: "folder",
        size_mb: 0,
        resolution: "获取信息失败",
        color_space: "文件夹",
      } as FileInfo;
    }

    dispatch("fileChange");
  }

  function clearSelection() {
    filePath = null;
    fileInfo = null;
    isFolder = false;
    imageFiles = [];
    dispatch("fileChange");
    // 通知外部状态变化
    if (onFileChange) {
      onFileChange(null, null);
    }
  }

  // Tauri 桌面端拖拽监听（新版 API）
  onMount(() => {
    let unlisten: (() => void) | undefined;
    getCurrentWindow()
      .onDragDropEvent((event) => {
        if (event.payload.type === "over") {
          isDragOver = true;
          console.log("User hovering", event.payload.position);
        } else if (event.payload.type === "drop") {
          isDragOver = false;
          console.log("User dropped", event.payload.paths);
          const files = event.payload.paths;
          if (files && files.length > 0) {
            const filePath = files[0];

            // 检查是否是文件夹（通过检查是否有文件扩展名）
            const hasExtension =
              filePath.includes(".") &&
              (filePath.split(".").pop()?.length || 0) > 0;

            if (!hasExtension) {
              // 可能是文件夹，直接处理
              handleFolder(filePath);
              return;
            }

            // 处理文件
            const ext = filePath.split(".").pop()?.toLowerCase();
            if (ext && SUPPORTED_EXTS.includes(ext)) {
              handleFile(filePath);
            } else {
              alert(
                "仅支持mp4, mov, avi, mkv, mp3, wav, aac, flac, m4a, ogg, wma, jpg, jpeg, png, bmp, gif, webp, tiff, tga格式的文件"
              );
            }
          }
        } else {
          isDragOver = false;
          console.log("File drop cancelled");
        }
      })
      .then((off) => {
        unlisten = off;
      });
    return () => {
      if (unlisten) unlisten();
    };
  });

  // 打开预览窗口
  export async function openPreview(actionId?: string) {
    if (!filePath) {
      console.error("没有选择文件，无法打开预览");
      return;
    }

    try {
      await invoke("create_preview_window", {
        videoPath: filePath,
        actionId: actionId || null,
      });
    } catch (error) {
      console.error("打开预览窗口失败:", error);
    }
  }

  // Expose the filePath and selection functions
  export function getFilePath() {
    return filePath;
  }

  export { selectFile, selectFolder };
</script>

<Card class="w-full">
  <CardHeader class="p-4 sm:p-6">
    <h3 class="text-lg font-semibold">文件选择</h3>
  </CardHeader>
  <CardContent class="space-y-4 p-4 sm:p-6">
    <div class="w-full">
      {#if !filePath}
        <div class="space-y-2">
          <div
            class={`border-2 border-dashed rounded-md transition-colors h-24 sm:h-28 flex items-center justify-center ${isDragOver ? "border-primary bg-primary/10" : "border-border bg-background"}`}
          >
            <div class="flex gap-2">
              <button
                class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-3 sm:px-4 py-2"
                on:click={selectFile}>选择文件(F1)</button
              >
              <button
                class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-3 sm:px-4 py-2"
                on:click={selectFolder}>选择文件夹(F2)</button
              >
            </div>
          </div>
          <div class="text-xs text-gray-400 text-center mt-2">
            支持视频、音频、图片文件选择
          </div>
        </div>
      {:else}
        <div class="space-y-4">
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <h4 class="font-medium text-sm sm:text-base">已选择</h4>
              <button
                class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 px-3"
                on:click={clearSelection}
              >
                重新选择
              </button>
            </div>
            <div
              class="text-sm text-muted-foreground truncate pr-2"
              title={fileInfo?.filename}
            >
              {fileInfo?.filename}
            </div>
          </div>

          {#if fileInfo}
            <div
              class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm"
            >
              <div>
                <span class="text-muted-foreground">格式:</span>
                <span class="ml-2">{fileInfo.format}</span>
              </div>
              <div>
                <span class="text-muted-foreground">大小:</span>
                <span class="ml-2">{fileInfo.size_mb.toFixed(2)} MB</span>
              </div>

              {#if fileInfo.type === "Video" && fileInfo.duration}
                <div>
                  <span class="text-muted-foreground">时长:</span>
                  <span class="ml-2">{formatDuration(fileInfo.duration)}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">视频编码:</span>
                  <span class="ml-2">{fileInfo.video_codec}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">音频编码:</span>
                  <span class="ml-2">{fileInfo.audio_codec}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">分辨率:</span>
                  <span class="ml-2">{fileInfo.resolution}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">帧率:</span>
                  <span class="ml-2">{fileInfo.fps}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">视频码率:</span>
                  <span class="ml-2">{fileInfo.video_bitrate}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">音频采样率:</span>
                  <span class="ml-2">{fileInfo.audio_sample_rate} Hz</span>
                </div>
                <div>
                  <span class="text-muted-foreground">音频声道:</span>
                  <span class="ml-2">{fileInfo.audio_channels}</span>
                </div>
              {:else if fileInfo.type === "Audio" && fileInfo.duration}
                <div>
                  <span class="text-muted-foreground">时长:</span>
                  <span class="ml-2">{formatDuration(fileInfo.duration)}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">音频编码:</span>
                  <span class="ml-2">{fileInfo.audio_codec}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">音频采样率:</span>
                  <span class="ml-2">{fileInfo.audio_sample_rate} Hz</span>
                </div>
                <div>
                  <span class="text-muted-foreground">音频声道:</span>
                  <span class="ml-2">{fileInfo.audio_channels}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">音频码率:</span>
                  <span class="ml-2">{fileInfo.audio_bitrate}</span>
                </div>
              {:else if fileInfo.type === "Image"}
                <div>
                  <span class="text-muted-foreground">类型:</span>
                  <span class="ml-2">
                    {isFolder ? fileInfo.color_space : "图片文件"}
                  </span>
                </div>
                <div>
                  <span class="text-muted-foreground">
                    {isFolder ? "文件数量" : "分辨率"}:
                  </span>
                  <span class="ml-2">{fileInfo.resolution}</span>
                </div>
                {#if !isFolder}
                  <div>
                    <span class="text-muted-foreground">颜色空间:</span>
                    <span class="ml-2">{fileInfo.color_space}</span>
                  </div>
                {/if}
              {/if}

              {#if isFolder && imageFiles.length > 0}
                <div class="col-span-full mt-3">
                  <div class="text-muted-foreground text-xs mb-2">
                    文件列表:
                  </div>
                  <div class="bg-muted/30 rounded p-2 text-xs">
                    <div class="text-muted-foreground">
                      {(() => {
                        if (imageFiles.length === 0) return "无文件";

                        // 尝试显示完整的文件列表
                        const fullList = imageFiles.join(", ");

                        // 如果完整列表不超过50字符，直接显示
                        if (fullList.length <= 50) {
                          return fullList;
                        }

                        // 超过50字符时使用省略格式
                        if (imageFiles.length === 1) {
                          // 单个文件名太长，截断显示
                          return imageFiles[0].length > 47
                            ? imageFiles[0].substring(0, 47) + "..."
                            : imageFiles[0];
                        }

                        if (imageFiles.length === 2) {
                          // 两个文件，尝试显示第一个和省略号
                          const first = imageFiles[0];
                          const pattern = `${first}, ...`;
                          if (pattern.length <= 50) {
                            return pattern;
                          } else {
                            // 第一个文件名太长，截断
                            const maxLen = 50 - 5; // 减去 ", ..." 的长度
                            return first.substring(0, maxLen) + ", ...";
                          }
                        }

                        // 多个文件时，显示 "first, ..., last" 格式
                        const first = imageFiles[0];
                        const last = imageFiles[imageFiles.length - 1];
                        const pattern = `${first}, ..., ${last}`;

                        if (pattern.length <= 50) {
                          return pattern;
                        } else {
                          // 如果还是太长，智能截断文件名
                          const separatorLen = 7; // ", ..., " 的长度
                          const availableLen = 50 - separatorLen;
                          const firstMaxLen = Math.floor(availableLen * 0.6); // 第一个文件名占60%
                          const lastMaxLen = availableLen - firstMaxLen;

                          const shortFirst =
                            first.length > firstMaxLen
                              ? first.substring(0, firstMaxLen - 3) + "..."
                              : first;
                          const shortLast =
                            last.length > lastMaxLen
                              ? last.substring(0, lastMaxLen - 3) + "..."
                              : last;

                          return `${shortFirst}, ..., ${shortLast}`;
                        }
                      })()}
                    </div>
                  </div>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      {/if}
    </div>
  </CardContent>
</Card>
