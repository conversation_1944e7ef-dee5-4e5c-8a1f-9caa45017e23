{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[18251603632334587267, "build_script_build", false, 1117729686340268701], [10755362358622467486, "build_script_build", false, 6726321520364200505], [3834743577069889284, "build_script_build", false, 12376994016759116645], [422130612855741759, "build_script_build", false, 13895965998484282604], [17218623086136245857, "build_script_build", false, 11269871191704078348]], "local": [{"RerunIfChanged": {"output": "debug\\build\\VideoIDE-67a9f374cc52a1f6\\output", "paths": ["tauri.conf.json", "capabilities", "lib\\ffmpeg\\ffmpeg.exe", "lib\\ffmpeg\\ffprobe.exe", "lib\\mpv\\libmpv-2.dll"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}