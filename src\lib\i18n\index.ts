// i18n 配置文件
import { browser } from '$app/environment';
import { init, register, locale, waitLocale } from 'svelte-i18n';

const defaultLocale = 'zh-CN';

// 注册语言包
register('zh-CN', () => import('./locales/zh-CN.json'));
register('en-US', () => import('./locales/en-US.json'));

// 初始化 i18n
init({
  fallbackLocale: defaultLocale,
  initialLocale: browser ? getInitialLocale() : defaultLocale,
});

/**
 * 获取初始语言设置
 */
function getInitialLocale(): string {
  // 优先使用本地存储的语言设置
  const stored = localStorage.getItem('app-locale');
  if (stored && ['zh-CN', 'en-US'].includes(stored)) {
    return stored;
  }

  // 其次使用浏览器语言
  const browserLang = navigator.language;
  
  // 语言映射
  const langMap: Record<string, string> = {
    'zh': 'zh-CN',
    'zh-CN': 'zh-CN',
    'zh-TW': 'zh-CN',
    'zh-HK': 'zh-CN',
    'en': 'en-US',
    'en-US': 'en-US',
    'en-GB': 'en-US',
  };

  return langMap[browserLang] || langMap[browserLang.split('-')[0]] || defaultLocale;
}

/**
 * 切换语言
 * @param newLocale 新语言代码
 */
export async function switchLocale(newLocale: string): Promise<void> {
  if (!['zh-CN', 'en-US'].includes(newLocale)) {
    console.warn(`Unsupported locale: ${newLocale}`);
    return;
  }

  // 设置新语言
  locale.set(newLocale);
  
  // 等待语言包加载完成
  await waitLocale(newLocale);
  
  // 保存到本地存储
  if (browser) {
    localStorage.setItem('app-locale', newLocale);
  }
}

/**
 * 获取当前语言
 */
export function getCurrentLocale(): string {
  let currentLocale = defaultLocale;
  locale.subscribe(value => {
    if (value) currentLocale = value;
  })();
  return currentLocale;
}

/**
 * 获取支持的语言列表
 */
export function getSupportedLocales(): Array<{ code: string; name: string; nativeName: string }> {
  return [
    { code: 'zh-CN', name: 'Chinese (Simplified)', nativeName: '简体中文' },
    { code: 'en-US', name: 'English (US)', nativeName: 'English' }
  ];
}

// 导出常用的 i18n 函数
export { locale, _ as t } from 'svelte-i18n';
