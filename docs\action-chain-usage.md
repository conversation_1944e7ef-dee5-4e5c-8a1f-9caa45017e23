# 动作链管理功能使用指南

## 功能概述

动作链管理功能允许用户保存、管理和重复使用视频处理动作的组合，提高工作效率。

## 主要功能

### 1. Tab界面
- **处理列表Tab**: 原有的动作组织和处理界面
- **动作链列表Tab**: 管理已保存的动作链

### 2. 动作链状态显示
在处理列表Tab顶部显示当前动作链状态：
- 动作链名称
- 是否有未保存的更改（显示*标记）

### 3. 动作链操作

#### 在处理列表Tab中：
- **新建**: 清空当前动作，开始新的动作链
- **保存**: 保存当前动作链
- **另存为**: 将当前动作链保存为新的动作链
- **清空**: 清除所有动作

#### 在动作链列表Tab中：
- **加载**: 加载动作链到处理列表
- **重命名**: 修改动作链名称
- **编辑描述**: 点击描述文本可编辑
- **导出**: 将动作链导出为JSON文件
- **删除**: 删除动作链
- **导入**: 从JSON文件导入动作链

## 使用流程

### 创建动作链
1. 在处理列表Tab中添加和配置动作
2. 点击"保存"按钮
3. 输入动作链名称和描述
4. 确认保存

### 使用已有动作链
1. 切换到动作链列表Tab
2. 找到需要的动作链
3. 点击"加载"按钮
4. 自动切换到处理列表Tab
5. 根据需要调整参数
6. 开始处理

### 管理动作链
1. 在动作链列表Tab中查看所有动作链
2. 可以重命名、编辑描述、导出或删除
3. 支持导入其他用户分享的动作链

## 数据存储

### 存储位置
- **Windows**: `C:\Users\<USER>\AppData\Roaming\videoide\action_chains\`
- **macOS**: `~/Library/Application Support/videoide/action_chains/`

### 文件结构
- 每个动作链保存为独立的JSON文件
- `.metadata.json`文件记录使用统计信息

### 动作链JSON格式
```json
{
  "name": "我的视频处理流程",
  "description": "常用的视频处理步骤",
  "version": "1.0",
  "actions": [
    {
      "id": "crop",
      "name": "截掉头部30秒",
      "parameters": {
        "startTime": "00:00:30",
        "endTime": "auto"
      },
      "order": 1
    }
  ]
}
```

## 注意事项

1. **文件路径检查**: 加载动作链时会检查文件路径有效性，无效路径会被清空
2. **自动重命名**: 导入同名动作链时会自动添加数字后缀
3. **未保存提醒**: 切换动作链或Tab时会提醒保存未保存的更改
4. **使用统计**: 系统会记录动作链的使用次数和最后使用时间

## 快捷键

- **Ctrl+S**: 快速保存当前动作链
- **Ctrl+N**: 新建动作链

## 故障排除

### 常见问题
1. **动作链加载失败**: 检查JSON文件格式是否正确
2. **文件路径无效**: 重新选择相关文件
3. **导入失败**: 确保JSON文件符合动作链格式要求

### 数据备份
建议定期备份`action_chains`目录，或使用导出功能备份重要的动作链。


### 测试结果
[x] 1. 目前文件存储在`c:\users\<USER>\AppData\Roaming\com.videoide.app\action_chains` 而不是`videoide\action_chains`, 解释下为什么要用`com.videoide.app`
    **解释**: 这是Tauri应用的标准行为，使用`tauri.conf.json`中定义的`identifier`字段作为应用标识符，确保应用数据目录的唯一性，避免与其他应用冲突。

[x] 2. 当我选择一个视频后, 切换到`动作链列表tab`, 然后切换回`处理列表tab` , 选择的文件没有了, 应该保持处理列表的所有状态包括选择的文件
    **已修复**: 将文件选择状态提升到主页面级别，VideoSelector组件支持外部状态管理，Tab切换时保持文件选择状态。


[x] 3. `处理链列表`导出一个`处理链`, 无法选择保存文件路径, 提示错误
    **已修复**: 将`open`对话框改为`save`对话框，现在可以正确选择保存路径。

[x] 4. `开始处理`旁边的`清空动作`按钮多余了, 现在上面已经有了`清空`按钮
    **已修复**: 移除了重复的清空动作按钮。

[x] 5. `清空`按钮点击后直接就清空了, 并未等待用户确认
    **已修复**: 使用web确认对话框替代原生confirm，提供更好的用户体验。

[x] 6. `加载`一个处理链后, 选择视频, 并点开始处理, 显示`处理错误： 动作"截掉头部"不支持输入类型"video"。支持的输入类型:`
    **已修复**: 添加了`getActionTypeInfo`函数，加载动作链时正确设置`inputTypes`和`outputTypes`。
[x] 7. `动作链列表tab`点击某个处理链`删除`按钮, 没等用户确认就已经删除
    **已修复**: 添加了web确认对话框，删除前需要用户确认。
[x] 8. `处理列表tab`的`新建`按钮还是用了tauri对话框, 没等确认就操作了, 和其他按钮一致使用web对话框
    **已修复**: 将原生confirm对话框替换为web确认对话框，同时修复了加载动作链时的确认对话框。

[x] 9. `处理列表tab`的`清空`按钮, 和预期不符 `清空：清除所有动作，但保持当前动作链名称`, 这时候只要用户不点`保存`, 依然没有修改动作链内容
    **已修复**: 修改清空逻辑，现在只清除动作但保持动作链名称，状态标记为已修改，用户需要手动保存才会真正修改动作链。
[x] 11. `处理列表tab`的`保存`按钮, 成功后弹出tauri的对话框, 与其他行为不一致, 不用tauri对话框, 无需对话框提示
    **已修复**: 移除了保存成功的alert弹窗，现在依靠状态栏的星号标记变化来反馈保存状态，用户体验更加流畅。

[x] 12. 使用次数计算逻辑错误，应该只在点击"开始处理"时才算一次使用，而不是保存或加载时
    **已修复**: 修改了使用次数计算逻辑，现在只在点击"开始处理"时才会增加使用次数，保存和加载动作链不会影响使用统计。

[x] 13. 在`动作链列表tab` , 当鼠标悬停在某个动作链标题或空白处(除了描述行, 因为描述行点击修改), 显示一个当前动作链的动作简介tip(一行一个动作名, 无需参数, 方便快速预览)
    **已实现**: 创建了Tooltip组件，在动作链名称区域悬停500ms后显示动作预览，包含最多10个动作名称，超出显示省略提示。