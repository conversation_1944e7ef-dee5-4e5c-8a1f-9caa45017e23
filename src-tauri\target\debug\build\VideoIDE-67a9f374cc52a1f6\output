cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=D:\webdev\videoide\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=app
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_videoide
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=D:\webdev\videoide\src-tauri\target\debug\build\VideoIDE-67a9f374cc52a1f6\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
cargo:rerun-if-changed=lib\ffmpeg\ffmpeg.exe
cargo:rerun-if-changed=lib\ffmpeg\ffprobe.exe
cargo:rerun-if-changed=lib\mpv\libmpv-2.dll
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\webdev\videoide\src-tauri\target\debug\build\VideoIDE-67a9f374cc52a1f6\out\resource.lib
