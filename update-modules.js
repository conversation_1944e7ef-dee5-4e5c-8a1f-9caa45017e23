// 批量更新动作模块的脚本
const fs = require('fs');
const path = require('path');

const modules = [
  'effect.ts',
  'encode.ts', 
  'filter.ts',
  'image.ts',
  'watermark.ts'
];

const basePath = 'src/lib/actions/categories';

modules.forEach(module => {
  const filePath = path.join(basePath, module);
  
  if (fs.existsSync(filePath)) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 添加导入
    if (!content.includes('applyActionMapping')) {
      content = content.replace(
        /import type { Action } from "\.\.\/\.\.\/types\/action";/,
        `import type { Action } from "../../types/action";\nimport { applyActionMapping } from '../action-mappings';`
      );
    }
    
    // 修改导出变量名
    content = content.replace(
      /export const (\w+Actions): Action\[\] = \[/,
      'const raw$1 = ['
    );
    
    // 添加新的导出
    content = content.replace(
      /\];$/,
      '];\n\n// 应用映射并导出\nexport const $1Actions: Action[] = raw$1.map(applyActionMapping);'
    );
    
    fs.writeFileSync(filePath, content);
    console.log(`Updated ${module}`);
  }
});
