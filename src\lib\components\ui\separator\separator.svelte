<script lang="ts">
  import { cn } from "$lib/utils";

  let {
    orientation = "horizontal",
    decorative = true,
    className,
    ...props
  } = $props<{
    orientation?: "horizontal" | "vertical";
    decorative?: boolean;
    className?: string;
    [key: string]: any;
  }>();
</script>

<div
  role={decorative ? "none" : "separator"}
  aria-orientation={orientation}
  class={cn(
    "shrink-0 bg-border",
    orientation === "horizontal" ? "h-[1px] w-full" : "h-full w-[1px]",
    className
  )}
  {...props}
></div>
