// 所有类别定义的汇总

import { CategoryIds } from "../constants";
import type { Category } from "../types/action";

// 定义所有类别
export const categories: Category[] = [
  {
    id: CategoryIds.TRIM,
    nameKey: "categories.trim.name",
    descriptionKey: "categories.trim.description",
    order: 10,
  },
  {
    id: CategoryIds.COLOR,
    nameKey: "categories.color.name",
    descriptionKey: "categories.color.description",
    order: 20,
  },
  {
    id: CategoryIds.AUDIO,
    nameKey: "categories.audio.name",
    descriptionKey: "categories.audio.description",
    order: 30,
  },
  {
    id: CategoryIds.TRANSFORM,
    nameKey: "categories.transform.name",
    descriptionKey: "categories.transform.description",
    order: 40,
  },
  {
    id: CategoryIds.EFFECT,
    nameKey: "categories.effect.name",
    descriptionKey: "categories.effect.description",
    order: 50,
  },
  {
    id: CategoryIds.WATERMARK,
    nameKey: "categories.watermark.name",
    descriptionKey: "categories.watermark.description",
    order: 60,
  },
  {
    id: CategoryIds.ENCODE,
    nameKey: "categories.encode.name",
    descriptionKey: "categories.encode.description",
    order: 70,
  },
  {
    id: CategoryIds.IMAGE,
    nameKey: "categories.image.name",
    descriptionKey: "categories.image.description",
    order: 80,
  },
  {
    id: CategoryIds.FILTER,
    nameKey: "categories.filter.name",
    descriptionKey: "categories.filter.description",
    order: 90,
  },
];

// 导出别名以保持兼容性
export const allCategories = categories;
