use ffmpeg_sidecar::command::FfmpegCommand;
use std::env;
use std::path::{Path, PathBuf};

/// FFmpeg配置管理
pub struct FfmpegConfig {
    pub ffmpeg_path: PathBuf,
    pub ffprobe_path: PathBuf,
}

impl FfmpegConfig {
    /// 获取FFmpeg配置实例
    pub fn new() -> Result<Self, String> {
        let base_path = Self::get_base_path()?;

        // 根据操作系统确定可执行文件扩展名
        let exe_extension = if cfg!(target_os = "windows") {
            ".exe"
        } else {
            "" // macOS和Linux不需要扩展名
        };

        // 根据操作系统确定FFmpeg库路径
        let ffmpeg_lib_path = if cfg!(target_os = "windows") {
            base_path.join("lib").join("ffmpeg")
        } else if cfg!(target_os = "macos") {
            // macOS: 通常在.app bundle的Contents/Resources目录下
            base_path.join("Contents").join("Resources").join("ffmpeg")
        } else {
            // Linux: 通常在lib目录下
            base_path.join("lib").join("ffmpeg")
        };

        let ffmpeg_path = ffmpeg_lib_path.join(format!("ffmpeg{}", exe_extension));
        let ffprobe_path = ffmpeg_lib_path.join(format!("ffprobe{}", exe_extension));

        // 验证文件是否存在
        if !ffmpeg_path.exists() {
            return Err(format!("FFmpeg可执行文件不存在: {}", ffmpeg_path.display()));
        }

        if !ffprobe_path.exists() {
            return Err(format!(
                "FFprobe可执行文件不存在: {}",
                ffprobe_path.display()
            ));
        }

        println!("✅ FFmpeg路径: {}", ffmpeg_path.display());
        println!("✅ FFprobe路径: {}", ffprobe_path.display());

        Ok(Self {
            ffmpeg_path,
            ffprobe_path,
        })
    }

    /// 获取应用程序基础路径
    fn get_base_path() -> Result<PathBuf, String> {
        // 在开发环境中，使用项目根目录
        if cfg!(debug_assertions) {
            // 开发环境：从src-tauri目录开始
            let current_exe = env::current_exe().map_err(|e| e.to_string())?;
            let mut base_path = current_exe.parent().unwrap().to_path_buf();

            // 尝试找到src-tauri目录
            while !base_path.join("src-tauri").exists() && base_path.parent().is_some() {
                base_path = base_path.parent().unwrap().to_path_buf();
            }

            if base_path.join("src-tauri").exists() {
                return Ok(base_path.join("src-tauri"));
            }

            // 如果找不到src-tauri目录，尝试当前工作目录
            let current_dir = env::current_dir().map_err(|e| e.to_string())?;
            if current_dir.join("src-tauri").exists() {
                return Ok(current_dir.join("src-tauri"));
            }

            // 最后尝试直接使用当前目录（如果已经在src-tauri目录中）
            if current_dir.file_name().and_then(|n| n.to_str()) == Some("src-tauri") {
                return Ok(current_dir);
            }

            return Err("无法找到src-tauri目录".to_string());
        } else {
            // 生产环境：使用可执行文件所在目录
            let current_exe = env::current_exe().map_err(|e| e.to_string())?;
            let exe_dir = current_exe
                .parent()
                .ok_or("无法获取可执行文件目录")?
                .to_path_buf();

            // 在macOS上，可执行文件可能在.app bundle内
            if cfg!(target_os = "macos") {
                // 检查是否在.app bundle内
                if let Some(app_bundle_path) = Self::find_app_bundle_path(&exe_dir) {
                    return Ok(app_bundle_path);
                }
            }

            Ok(exe_dir)
        }
    }

    /// 在macOS上查找.app bundle路径
    #[cfg(target_os = "macos")]
    fn find_app_bundle_path(exe_dir: &Path) -> Option<PathBuf> {
        let mut current = exe_dir;
        
        // 向上查找.app bundle
        while let Some(parent) = current.parent() {
            if current.file_name()
                .and_then(|n| n.to_str())
                .map(|s| s.ends_with(".app"))
                .unwrap_or(false)
            {
                return Some(current.to_path_buf());
            }
            current = parent;
        }
        
        None
    }

    #[cfg(not(target_os = "macos"))]
    fn find_app_bundle_path(_exe_dir: &Path) -> Option<PathBuf> {
        None
    }

    /// 获取FFmpeg可执行文件路径字符串
    pub fn ffmpeg_path_str(&self) -> Result<String, String> {
        self.ffmpeg_path
            .to_str()
            .ok_or("FFmpeg路径包含无效字符".to_string())
            .map(|s| s.to_string())
    }

    /// 获取FFprobe可执行文件路径字符串
    pub fn ffprobe_path_str(&self) -> Result<String, String> {
        self.ffprobe_path
            .to_str()
            .ok_or("FFprobe路径包含无效字符".to_string())
            .map(|s| s.to_string())
    }
}

/// 全局FFmpeg配置实例
static mut FFMPEG_CONFIG: Option<FfmpegConfig> = None;
static INIT_ONCE: std::sync::Once = std::sync::Once::new();

/// 初始化FFmpeg配置
pub fn init_ffmpeg_config() -> Result<(), String> {
    INIT_ONCE.call_once(|| match FfmpegConfig::new() {
        Ok(config) => {
            unsafe {
                FFMPEG_CONFIG = Some(config);
            }
            println!("✅ FFmpeg配置初始化成功");
        }
        Err(e) => {
            eprintln!("❌ FFmpeg配置初始化失败: {}", e);
        }
    });

    unsafe {
        if FFMPEG_CONFIG.is_some() {
            Ok(())
        } else {
            Err("FFmpeg配置初始化失败".to_string())
        }
    }
}

/// 获取FFmpeg可执行文件路径
pub fn get_ffmpeg_path() -> Result<String, String> {
    unsafe {
        FFMPEG_CONFIG
            .as_ref()
            .ok_or("FFmpeg配置未初始化".to_string())?
            .ffmpeg_path_str()
    }
}

/// 获取FFprobe可执行文件路径
pub fn get_ffprobe_path() -> Result<String, String> {
    unsafe {
        FFMPEG_CONFIG
            .as_ref()
            .ok_or("FFprobe配置未初始化".to_string())?
            .ffprobe_path_str()
    }
}

/// 检查FFmpeg是否可用
pub fn check_ffmpeg_available() -> bool {
    match get_ffmpeg_path() {
        Ok(path) => Path::new(&path).exists(),
        Err(_) => false,
    }
}

/// 检查FFprobe是否可用
pub fn check_ffprobe_available() -> bool {
    match get_ffprobe_path() {
        Ok(path) => Path::new(&path).exists(),
        Err(_) => false,
    }
}

/// 创建使用指定路径的FFmpeg命令
/// 参考: https://github.com/nathanbabcock/ffmpeg-sidecar/issues/41
pub fn create_ffmpeg_command() -> Result<FfmpegCommand, String> {
    let ffmpeg_path = get_ffmpeg_path()?;
    Ok(FfmpegCommand::new_with_path(&ffmpeg_path))
}

/// 创建使用指定路径的FFprobe命令
pub fn create_ffprobe_command() -> Result<tokio::process::Command, String> {
    let ffprobe_path = get_ffprobe_path()?;
    let mut cmd = tokio::process::Command::new(&ffprobe_path);

    #[cfg(windows)]
    {
        use std::os::windows::process::CommandExt;
        cmd.creation_flags(0x08000000); // CREATE_NO_WINDOW
    }

    Ok(cmd)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_os_specific_paths() {
        // 测试可执行文件扩展名
        let exe_extension = if cfg!(target_os = "windows") {
            ".exe"
        } else {
            ""
        };

        if cfg!(target_os = "windows") {
            assert_eq!(exe_extension, ".exe");
        } else {
            assert_eq!(exe_extension, "");
        }

        // 测试路径构建
        let base_path = PathBuf::from("/test/base");
        let ffmpeg_lib_path = if cfg!(target_os = "windows") {
            base_path.join("lib").join("ffmpeg")
        } else if cfg!(target_os = "macos") {
            base_path.join("Contents").join("Resources").join("ffmpeg")
        } else {
            base_path.join("lib").join("ffmpeg")
        };

        let ffmpeg_path = ffmpeg_lib_path.join(format!("ffmpeg{}", exe_extension));
        let ffprobe_path = ffmpeg_lib_path.join(format!("ffprobe{}", exe_extension));

        if cfg!(target_os = "windows") {
            assert!(ffmpeg_path.to_string_lossy().ends_with("ffmpeg.exe"));
            assert!(ffprobe_path.to_string_lossy().ends_with("ffprobe.exe"));
        } else {
            assert!(ffmpeg_path.to_string_lossy().ends_with("ffmpeg"));
            assert!(ffprobe_path.to_string_lossy().ends_with("ffprobe"));
        }

        if cfg!(target_os = "macos") {
            assert!(ffmpeg_path.to_string_lossy().contains("Contents/Resources"));
        } else if cfg!(target_os = "windows") {
            assert!(ffmpeg_path.to_string_lossy().contains("lib\\ffmpeg"));
        } else {
            assert!(ffmpeg_path.to_string_lossy().contains("lib/ffmpeg"));
        }
    }

    #[test]
    fn test_app_bundle_detection() {
        // 测试macOS上的.app bundle检测
        if cfg!(target_os = "macos") {
            let test_path = PathBuf::from("/Applications/TestApp.app/Contents/MacOS");
            let app_bundle = FfmpegConfig::find_app_bundle_path(&test_path);
            assert!(app_bundle.is_some());
            assert!(app_bundle.unwrap().to_string_lossy().ends_with("TestApp.app"));
        } else {
            let test_path = PathBuf::from("/test/path");
            let app_bundle = FfmpegConfig::find_app_bundle_path(&test_path);
            assert!(app_bundle.is_none());
        }
    }
}
