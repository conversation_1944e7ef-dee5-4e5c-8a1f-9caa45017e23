use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

// 模块声明
pub mod action_chain;
pub mod audio;
pub mod color;
pub mod common;
pub mod effect;
pub mod encode;
pub mod filter;
pub mod image;
pub mod player;
pub mod transform;
pub mod trim;
pub mod videotoimage;
pub mod watermark;
pub mod config;

// 导入trim模块的函数
use trim::{
    concat_videos, crop_video, exclude_and_concat_segments, trim_and_concat_segments,
    trim_video_end, trim_video_segment, trim_video_start,
};

// 导入color模块的函数
use color::{
    adjust_brightness, adjust_contrast, adjust_gamma, adjust_hue, adjust_saturation,
    adjust_white_balance,
};

// 导入transform模块的函数
use transform::{flip_video_horizontal, flip_video_vertical, rotate_video, scale_video};

// 导入audio模块的函数
use audio::{add_bgmusic, adjust_volume, audio_replace, extract_audio, mute_audio};

// 导入image模块的函数
use image::{
    add_cover_image, add_end_image, batch_images_to_video, get_folder_info,
    get_image_files_in_folder, image_to_video,
};

// 导入effect模块的函数
use effect::{adjust_speed, blur_video, fade_in, fade_out, reverse_video, sharpen_video};

// 导入watermark模块的函数
use watermark::{add_image_watermark, add_text_watermark, add_video_watermark};

// 导入encode模块的函数
use encode::{adjust_bitrate, adjust_resolution, compress_video, convert_format};

// 导入videotoimage模块的函数
use videotoimage::{video_to_gif, video_to_images};

// 导入player模块的函数
use player::commands::{
    cleanup_mpv_player, emit_segments_to_main, init_mpv_player, preview_mpv_frame_back_step,
    preview_mpv_frame_step, preview_mpv_get_duration, preview_mpv_get_mute,
    preview_mpv_get_position, preview_mpv_get_speed, preview_mpv_get_volume, preview_mpv_is_paused,
    preview_mpv_load_file, preview_mpv_pause, preview_mpv_play, preview_mpv_seek,
    preview_mpv_seek_relative, preview_mpv_set_mute, preview_mpv_set_speed, preview_mpv_set_volume,
    preview_mpv_stop,
};
use player::window_manager::{create_preview_window, sync_mpv_window_position};

// 导入action_chain模块的函数
use action_chain::commands::{
    delete_action_chain, export_action_chain, import_action_chain, init_action_chain_manager,
    list_action_chains, load_action_chain, rename_action_chain, save_action_chain,
    update_action_chain_description, update_action_chain_usage, ActionChainManagerState,
};

// 导入common模块的函数和结构体
use common::{
    get_audio_info_internal, get_image_info_internal, get_video_info_internal, kill_ffmpeg_process,
    set_cancelled, FileInfo, VideoInfo,
};

// 取消处理命令
#[tauri::command]
async fn cancel_processing() -> Result<(), String> {
    println!("取消处理...");
    set_cancelled();
    kill_ffmpeg_process();
    println!("处理已取消");
    Ok(())
}

#[tauri::command]
async fn get_video_info(file_path: String) -> Result<VideoInfo, String> {
    get_video_info_internal(&file_path).await
}

// 通用文件信息获取函数
#[tauri::command]
async fn get_file_info(file_path: String) -> Result<FileInfo, String> {
    let path = std::path::Path::new(&file_path);
    let extension = path
        .extension()
        .and_then(|s| s.to_str())
        .unwrap_or("")
        .to_lowercase();

    // 根据文件扩展名判断文件类型
    let video_exts = ["mp4", "mov", "avi", "mkv", "wmv", "flv", "webm", "m4v"];
    let audio_exts = ["mp3", "wav", "aac", "flac", "m4a", "ogg", "wma"];
    let image_exts = ["jpg", "jpeg", "png", "bmp", "gif", "webp", "tiff", "tga"];

    if video_exts.contains(&extension.as_str()) {
        let video_info = get_video_info_internal(&file_path).await?;
        Ok(FileInfo::Video(video_info))
    } else if audio_exts.contains(&extension.as_str()) {
        let audio_info = get_audio_info_internal(&file_path).await?;
        Ok(FileInfo::Audio(audio_info))
    } else if image_exts.contains(&extension.as_str()) {
        let image_info = get_image_info_internal(&file_path).await?;
        Ok(FileInfo::Image(image_info))
    } else {
        Err("不支持的文件格式".to_string())
    }
}

#[tauri::command]
async fn delete_file(file_path: String) -> Result<(), String> {
    if Path::new(&file_path).exists() {
        std::fs::remove_file(&file_path).map_err(|e| e.to_string())?;
        println!("已删除临时文件: {}", file_path);
    }
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化FFmpeg配置
    if let Err(e) = config::init_ffmpeg_config() {
        eprintln!("❌ FFmpeg配置初始化失败: {}", e);
        // 在这里可以决定是否要恐慌或优雅地关闭
    }

    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_drag::init())
        .manage(ActionChainManagerState::default())
        .invoke_handler(tauri::generate_handler![
            trim_video_start,
            trim_video_end,
            trim_video_segment,
            trim_and_concat_segments,
            exclude_and_concat_segments,
            concat_videos,
            get_video_info,
            get_file_info,
            adjust_brightness,
            adjust_contrast,
            adjust_saturation,
            adjust_hue,
            adjust_gamma,
            adjust_white_balance,
            delete_file,
            adjust_volume,
            mute_audio,
            extract_audio,
            add_bgmusic,
            audio_replace,
            crop_video,
            rotate_video,
            flip_video_horizontal,
            flip_video_vertical,
            scale_video,
            cancel_processing,
            add_cover_image,
            add_end_image,
            image_to_video,
            batch_images_to_video,
            get_image_files_in_folder,
            get_folder_info,
            reverse_video,
            adjust_speed,
            fade_in,
            fade_out,
            blur_video,
            sharpen_video,
            filter::filter_grayscale,
            filter::filter_sepia,
            filter::filter_emboss,
            filter::filter_sketch,
            filter::filter_oilpaint,
            filter::filter_mosaic,
            filter::filter_pixelate,
            filter::filter_edge_detect,
            filter::filter_cool,
            filter::filter_warm,
            add_text_watermark,
            add_image_watermark,
            add_video_watermark,
            convert_format,
            compress_video,
            adjust_bitrate,
            adjust_resolution,
            video_to_images,
            video_to_gif,
            preview_mpv_load_file,
            preview_mpv_play,
            preview_mpv_pause,
            preview_mpv_stop,
            preview_mpv_seek,
            preview_mpv_seek_relative,
            preview_mpv_get_position,
            preview_mpv_get_duration,
            preview_mpv_is_paused,
            preview_mpv_set_speed,
            preview_mpv_get_speed,
            preview_mpv_frame_step,
            preview_mpv_frame_back_step,
            preview_mpv_set_volume,
            preview_mpv_get_volume,
            preview_mpv_set_mute,
            preview_mpv_get_mute,
            create_preview_window,
            init_mpv_player,
            cleanup_mpv_player,
            sync_mpv_window_position,
            emit_segments_to_main,
            init_action_chain_manager,
            save_action_chain,
            load_action_chain,
            delete_action_chain,
            rename_action_chain,
            list_action_chains,
            update_action_chain_usage,
            export_action_chain,
            import_action_chain,
            update_action_chain_description,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
