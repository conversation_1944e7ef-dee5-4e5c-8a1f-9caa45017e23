pub mod commands;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use tauri::Manager;

// 动作链数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ActionChain {
    pub name: String,
    pub description: String,
    pub version: String,
    pub actions: Vec<ActionData>,
}

// 动作数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ActionData {
    pub id: String,
    pub name: String,
    pub parameters: serde_json::Value,
    pub order: u32,
}

// 动作链信息（用于列表显示）
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ActionChainInfo {
    pub name: String,
    pub description: String,
    pub action_count: u32,
    pub created_at: String,
    pub last_used_at: Option<String>,
    pub use_count: u32,
    pub action_names: Vec<String>, // 动作名称列表，用于预览
}

// 使用统计数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct UsageStats {
    pub created_at: String,
    pub last_used_at: Option<String>,
    pub use_count: u32,
}

// 元数据管理器
pub struct ActionChainManager {
    data_dir: PathBuf,
    metadata_file: PathBuf,
    metadata: HashMap<String, UsageStats>,
}

impl ActionChainManager {
    pub fn new(app_handle: &tauri::AppHandle) -> Result<Self, String> {
        // 获取应用数据目录
        let app_data = app_handle
            .path()
            .app_data_dir()
            .map_err(|e| format!("无法获取应用数据目录: {}", e))?;

        let data_dir = app_data.join("action_chains");
        let metadata_file = data_dir.join(".metadata.json");

        // 创建目录（如果不存在）
        fs::create_dir_all(&data_dir).map_err(|e| format!("创建数据目录失败: {}", e))?;

        let mut manager = Self {
            data_dir,
            metadata_file,
            metadata: HashMap::new(),
        };

        // 加载元数据
        manager.load_metadata()?;
        Ok(manager)
    }

    // 加载元数据
    fn load_metadata(&mut self) -> Result<(), String> {
        if self.metadata_file.exists() {
            let content = fs::read_to_string(&self.metadata_file)
                .map_err(|e| format!("读取元数据文件失败: {}", e))?;

            self.metadata =
                serde_json::from_str(&content).map_err(|e| format!("解析元数据失败: {}", e))?;
        }
        Ok(())
    }

    // 保存元数据
    fn save_metadata(&self) -> Result<(), String> {
        let content = serde_json::to_string_pretty(&self.metadata)
            .map_err(|e| format!("序列化元数据失败: {}", e))?;

        fs::write(&self.metadata_file, content)
            .map_err(|e| format!("保存元数据文件失败: {}", e))?;

        Ok(())
    }

    // 获取动作链文件路径
    fn get_chain_file_path(&self, name: &str) -> PathBuf {
        self.data_dir.join(format!("{}.json", name))
    }

    // 保存动作链
    pub fn save_action_chain(&mut self, chain: &ActionChain) -> Result<(), String> {
        let file_path = self.get_chain_file_path(&chain.name);

        // 序列化动作链
        let content =
            serde_json::to_string_pretty(chain).map_err(|e| format!("序列化动作链失败: {}", e))?;

        // 写入文件
        fs::write(&file_path, content).map_err(|e| format!("保存动作链文件失败: {}", e))?;

        // 更新元数据（保存时不增加使用次数，只更新时间）
        let now = chrono::Utc::now().to_rfc3339();
        let filename = format!("{}.json", chain.name);

        if let Some(stats) = self.metadata.get_mut(&filename) {
            stats.last_used_at = Some(now);
            // 保存时不增加使用次数
        } else {
            self.metadata.insert(
                filename,
                UsageStats {
                    created_at: now.clone(),
                    last_used_at: Some(now),
                    use_count: 0, // 初始使用次数为0
                },
            );
        }

        self.save_metadata()?;
        Ok(())
    }

    // 加载动作链
    pub fn load_action_chain(&mut self, name: &str) -> Result<ActionChain, String> {
        let file_path = self.get_chain_file_path(name);

        if !file_path.exists() {
            return Err(format!("动作链 '{}' 不存在", name));
        }

        let content =
            fs::read_to_string(&file_path).map_err(|e| format!("读取动作链文件失败: {}", e))?;

        let chain: ActionChain =
            serde_json::from_str(&content).map_err(|e| format!("解析动作链失败: {}", e))?;

        // 加载时不更新使用统计，只在实际处理时才更新

        Ok(chain)
    }

    // 删除动作链
    pub fn delete_action_chain(&mut self, name: &str) -> Result<(), String> {
        let file_path = self.get_chain_file_path(name);

        if !file_path.exists() {
            return Err(format!("动作链 '{}' 不存在", name));
        }

        // 删除文件
        fs::remove_file(&file_path).map_err(|e| format!("删除动作链文件失败: {}", e))?;

        // 删除元数据
        let filename = format!("{}.json", name);
        self.metadata.remove(&filename);
        self.save_metadata()?;

        Ok(())
    }

    // 重命名动作链
    pub fn rename_action_chain(&mut self, old_name: &str, new_name: &str) -> Result<(), String> {
        let old_file_path = self.get_chain_file_path(old_name);
        let new_file_path = self.get_chain_file_path(new_name);

        if !old_file_path.exists() {
            return Err(format!("动作链 '{}' 不存在", old_name));
        }

        if new_file_path.exists() {
            return Err(format!("动作链 '{}' 已存在", new_name));
        }

        // 读取并更新动作链内容
        let content =
            fs::read_to_string(&old_file_path).map_err(|e| format!("读取动作链文件失败: {}", e))?;

        let mut chain: ActionChain =
            serde_json::from_str(&content).map_err(|e| format!("解析动作链失败: {}", e))?;

        chain.name = new_name.to_string();

        // 保存到新文件
        let new_content =
            serde_json::to_string_pretty(&chain).map_err(|e| format!("序列化动作链失败: {}", e))?;

        fs::write(&new_file_path, new_content)
            .map_err(|e| format!("保存新动作链文件失败: {}", e))?;

        // 删除旧文件
        fs::remove_file(&old_file_path).map_err(|e| format!("删除旧动作链文件失败: {}", e))?;

        // 更新元数据
        let old_filename = format!("{}.json", old_name);
        let new_filename = format!("{}.json", new_name);

        if let Some(stats) = self.metadata.remove(&old_filename) {
            self.metadata.insert(new_filename, stats);
        }

        self.save_metadata()?;
        Ok(())
    }

    // 获取动作链列表
    pub fn list_action_chains(&self) -> Result<Vec<ActionChainInfo>, String> {
        let mut chains = Vec::new();

        // 遍历数据目录中的JSON文件
        let entries =
            fs::read_dir(&self.data_dir).map_err(|e| format!("读取数据目录失败: {}", e))?;

        for entry in entries {
            let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
            let path = entry.path();

            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                let filename = path
                    .file_name()
                    .and_then(|s| s.to_str())
                    .unwrap_or("")
                    .to_string();

                // 跳过元数据文件
                if filename == ".metadata.json" {
                    continue;
                }

                // 读取动作链文件
                if let Ok(content) = fs::read_to_string(&path) {
                    if let Ok(chain) = serde_json::from_str::<ActionChain>(&content) {
                        let stats = self.metadata.get(&filename);

                        // 提取动作名称列表
                        let action_names: Vec<String> = chain
                            .actions
                            .iter()
                            .map(|action| action.name.clone())
                            .collect();

                        chains.push(ActionChainInfo {
                            name: chain.name,
                            description: chain.description,
                            action_count: chain.actions.len() as u32,
                            created_at: stats
                                .map(|s| s.created_at.clone())
                                .unwrap_or_else(|| "未知".to_string()),
                            last_used_at: stats.and_then(|s| s.last_used_at.clone()),
                            use_count: stats.map(|s| s.use_count).unwrap_or(0),
                            action_names,
                        });
                    }
                }
            }
        }

        // 按最后使用时间排序
        chains.sort_by(|a, b| match (&a.last_used_at, &b.last_used_at) {
            (Some(a_time), Some(b_time)) => b_time.cmp(a_time),
            (Some(_), None) => std::cmp::Ordering::Less,
            (None, Some(_)) => std::cmp::Ordering::Greater,
            (None, None) => a.name.cmp(&b.name),
        });

        Ok(chains)
    }

    // 更新使用统计
    pub fn update_usage_stats(&mut self, name: &str) -> Result<(), String> {
        let filename = format!("{}.json", name);
        let now = chrono::Utc::now().to_rfc3339();

        if let Some(stats) = self.metadata.get_mut(&filename) {
            stats.last_used_at = Some(now);
            stats.use_count += 1;
        } else {
            self.metadata.insert(
                filename,
                UsageStats {
                    created_at: now.clone(),
                    last_used_at: Some(now),
                    use_count: 1,
                },
            );
        }

        self.save_metadata()?;
        Ok(())
    }

    // 导出动作链
    pub fn export_action_chain(&self, name: &str, export_path: &str) -> Result<(), String> {
        let file_path = self.get_chain_file_path(name);

        if !file_path.exists() {
            return Err(format!("动作链 '{}' 不存在", name));
        }

        fs::copy(&file_path, export_path).map_err(|e| format!("导出动作链失败: {}", e))?;

        Ok(())
    }

    // 导入动作链
    pub fn import_action_chain(&mut self, import_path: &str) -> Result<String, String> {
        // 读取导入文件
        let content =
            fs::read_to_string(import_path).map_err(|e| format!("读取导入文件失败: {}", e))?;

        // 解析动作链
        let mut chain: ActionChain =
            serde_json::from_str(&content).map_err(|e| format!("解析导入文件失败: {}", e))?;

        // 检查同名冲突并自动重命名
        let mut final_name = chain.name.clone();
        let mut counter = 2;

        while self.get_chain_file_path(&final_name).exists() {
            final_name = format!("{}{}", chain.name, counter);
            counter += 1;
        }

        chain.name = final_name.clone();

        // 保存动作链
        self.save_action_chain(&chain)?;

        Ok(final_name)
    }
}
