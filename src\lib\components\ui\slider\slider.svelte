<script lang="ts">
  import { cn } from "$lib/utils";

  interface $$Props {
    value?: number;
    min?: number;
    max?: number;
    step?: number;
    disabled?: boolean;
    class?: string;
    onchange?: (value: number) => void;
  }

  let {
    value = 0,
    min = 0,
    max = 100,
    step = 1,
    disabled = false,
    class: className = "",
    onchange,
  }: $$Props = $props();

  let sliderRef: HTMLDivElement;
  let isDragging = false;

  function updateValue(clientX: number) {
    if (!sliderRef || disabled) return;

    const rect = sliderRef.getBoundingClientRect();
    const percentage = Math.max(
      0,
      Math.min(1, (clientX - rect.left) / rect.width)
    );
    const newValue = min + percentage * (max - min);

    // 应用步长
    const steppedValue = Math.round(newValue / step) * step;
    const clampedValue = Math.max(min, Math.min(max, steppedValue));

    if (clampedValue !== value) {
      value = clampedValue;
    }
  }

  function handleMouseDown(event: MouseEvent) {
    if (disabled) return;

    isDragging = true;
    updateValue(event.clientX);

    function handleMouseMove(event: MouseEvent) {
      if (isDragging) {
        updateValue(event.clientX);
      }
    }

    function handleMouseUp() {
      if (isDragging) {
        isDragging = false;
        onchange?.(value);
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      }
    }

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  }

  function handleClick(event: MouseEvent) {
    if (disabled || isDragging) return;
    updateValue(event.clientX);
    onchange?.(value);
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (disabled) return;

    let newValue = value;
    switch (event.key) {
      case "ArrowLeft":
      case "ArrowDown":
        newValue = Math.max(min, value - step);
        break;
      case "ArrowRight":
      case "ArrowUp":
        newValue = Math.min(max, value + step);
        break;
      case "Home":
        newValue = min;
        break;
      case "End":
        newValue = max;
        break;
      default:
        return;
    }

    event.preventDefault();
    if (newValue !== value) {
      value = newValue;
      onchange?.(value);
    }
  }

  const percentage = $derived(((value - min) / (max - min)) * 100);
</script>

<div
  bind:this={sliderRef}
  class={cn(
    "relative flex w-full touch-none select-none items-center cursor-pointer",
    disabled && "cursor-not-allowed opacity-50",
    className
  )}
  role="slider"
  aria-valuemin={min}
  aria-valuemax={max}
  aria-valuenow={value}
  aria-disabled={disabled}
  tabindex={disabled ? -1 : 0}
  onmousedown={handleMouseDown}
  onclick={handleClick}
  onkeydown={handleKeyDown}
>
  <!-- 滑轨 -->
  <div
    class="relative h-2 w-full grow overflow-hidden rounded-full bg-secondary"
  >
    <!-- 已填充部分 -->
    <div
      class="absolute h-full bg-primary transition-all duration-150 ease-out"
      style="width: {percentage}%"
    ></div>
  </div>

  <!-- 滑块 -->
  <div
    class="absolute block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
    style="left: calc({percentage}% - 10px)"
  ></div>
</div>
