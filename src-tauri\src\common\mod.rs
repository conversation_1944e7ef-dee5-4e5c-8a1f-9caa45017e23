use serde::{Deserialize, Serialize};
use std::process::Command;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Mutex;

use crate::config::create_ffprobe_command;

// 全局取消标志
static CANCELLED: AtomicBool = AtomicBool::new(false);

// 全局ffmpeg进程管理标志
static FFMPEG_RUNNING: Mutex<bool> = Mutex::new(false);

static KILL_ATTEMPTED: AtomicBool = AtomicBool::new(false);

#[derive(Serialize, Deserialize)]
pub struct VideoInfo {
    pub filename: String,
    pub duration: f64,
    pub format: String,
    pub size_mb: f64,
    pub video_codec: String,
    pub resolution: String,
    pub fps: String,
    pub video_bitrate: String,
    pub audio_codec: String,
    pub audio_sample_rate: String,
    pub audio_channels: i64,
}

#[derive(Serialize, Deserialize)]
pub struct AudioInfo {
    pub filename: String,
    pub duration: f64,
    pub format: String,
    pub size_mb: f64,
    pub audio_codec: String,
    pub audio_sample_rate: String,
    pub audio_channels: i64,
    pub audio_bitrate: String,
}

#[derive(Serialize, Deserialize)]
pub struct ImageInfo {
    pub filename: String,
    pub format: String,
    pub size_mb: f64,
    pub resolution: String,
    pub color_space: String,
}

#[derive(Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum FileInfo {
    Video(VideoInfo),
    Audio(AudioInfo),
    Image(ImageInfo),
}

// 检查是否已取消
pub fn is_cancelled() -> bool {
    CANCELLED.load(Ordering::Relaxed)
}

// 重置取消标志
pub fn reset_cancelled() {
    CANCELLED.store(false, Ordering::Relaxed);
    reset_kill_attempted();
}

// 设置取消标志
pub fn set_cancelled() {
    CANCELLED.store(true, Ordering::Relaxed);
}

// 标记ffmpeg进程开始运行
pub fn mark_ffmpeg_running() {
    if let Ok(mut guard) = FFMPEG_RUNNING.lock() {
        *guard = true;
    }
}

// 标记ffmpeg进程结束
pub fn mark_ffmpeg_finished() {
    if let Ok(mut guard) = FFMPEG_RUNNING.lock() {
        *guard = false;
    }
}

fn reset_kill_attempted() {
    KILL_ATTEMPTED.store(false, Ordering::Relaxed);
}

// 强制终止所有ffmpeg进程
pub fn kill_ffmpeg_process() {
    if KILL_ATTEMPTED.swap(true, Ordering::Relaxed) {
        // 已经kill过, 不再重复kill
        return;
    }
    println!("正在终止ffmpeg进程...");
    #[cfg(windows)]
    {
        let output = Command::new("taskkill")
            .args(["/F", "/IM", "ffmpeg.exe"])
            .output();

        match output {
            Ok(result) => {
                if result.status.success() {
                    println!("✅ ffmpeg进程已终止");
                } else {
                    println!(
                        "❌ 终止ffmpeg进程失败: {}",
                        String::from_utf8_lossy(&result.stderr)
                    );
                }
            }
            Err(e) => {
                println!("❌ 终止ffmpeg进程出错: {}", e);
            }
        }
    }
    #[cfg(not(windows))]
    {
        let output = Command::new("pkill").args(["-f", "ffmpeg"]).output();

        match output {
            Ok(result) => {
                if result.status.success() {
                    println!("✅ ffmpeg进程已终止");
                } else {
                    println!(
                        "❌ 终止ffmpeg进程失败: {}",
                        String::from_utf8_lossy(&result.stderr)
                    );
                }
            }
            Err(e) => {
                println!("❌ 终止ffmpeg进程出错: {}", e);
            }
        }
    }
    mark_ffmpeg_finished();
}

// 内部函数，用于获取视频信息
pub async fn get_video_info_internal(file_path: &str) -> Result<VideoInfo, String> {
    let mut cmd = create_ffprobe_command()?;
    cmd.args([
        "-v",
        "quiet",
        "-print_format",
        "json",
        "-show_format",
        "-show_streams",
        file_path,
    ]);
    let output = cmd.output().await.map_err(|e| e.to_string())?;

    if !output.status.success() {
        return Err("FFprobe执行失败".to_string());
    }

    let json_str = String::from_utf8(output.stdout).map_err(|e| e.to_string())?;
    let info: serde_json::Value = serde_json::from_str(&json_str).map_err(|e| e.to_string())?;

    // 解析并构建 VideoInfo 结构
    let mut video_info = VideoInfo {
        filename: file_path.split('/').last().unwrap_or("").to_string(),
        duration: 0.0,
        format: std::path::Path::new(file_path)
            .extension()
            .and_then(|s| s.to_str())
            .unwrap_or("")
            .to_lowercase(),
        size_mb: 0.0,
        video_codec: String::new(),
        resolution: String::new(),
        fps: String::new(),
        video_bitrate: String::new(),
        audio_codec: String::new(),
        audio_sample_rate: String::new(),
        audio_channels: 0,
    };

    // 填充格式信息
    if let Some(format) = info["format"].as_object() {
        video_info.duration = format["duration"]
            .as_str()
            .unwrap_or("0")
            .parse()
            .unwrap_or(0.0);
        let size_bytes: u64 = format["size"].as_str().unwrap_or("0").parse().unwrap_or(0);
        video_info.size_mb = size_bytes as f64 / 1024.0 / 1024.0;
    }

    // 填充流信息
    if let Some(streams) = info["streams"].as_array() {
        for stream in streams {
            match stream["codec_type"].as_str().unwrap_or("") {
                "video" => {
                    video_info.video_codec =
                        stream["codec_name"].as_str().unwrap_or("未知").to_string();
                    let width = stream["width"].as_i64().unwrap_or(0);
                    let height = stream["height"].as_i64().unwrap_or(0);
                    video_info.resolution = format!("{}x{}", width, height);
                    let r_frame_rate = stream["r_frame_rate"].as_str().unwrap_or("0/1");
                    let fps_val = if let Some((num, den)) = r_frame_rate.split_once('/') {
                        let n: f64 = num.parse().unwrap_or(0.0);
                        let d: f64 = den.parse().unwrap_or(1.0);
                        if d != 0.0 {
                            n / d
                        } else {
                            0.0
                        }
                    } else {
                        0.0
                    };
                    video_info.fps = format!("{:.2}", fps_val);
                    if let Some(bit_rate) = stream["bit_rate"].as_str() {
                        let kbps = bit_rate.parse::<i64>().unwrap_or(0) / 1000;
                        video_info.video_bitrate = format!("{} kbps", kbps);
                    }
                }
                "audio" => {
                    video_info.audio_codec =
                        stream["codec_name"].as_str().unwrap_or("未知").to_string();
                    video_info.audio_sample_rate =
                        stream["sample_rate"].as_str().unwrap_or("0").to_string();
                    video_info.audio_channels = stream["channels"].as_i64().unwrap_or(0);
                }
                _ => {}
            }
        }
    }

    Ok(video_info)
}

// 获取音频文件信息
pub async fn get_audio_info_internal(file_path: &str) -> Result<AudioInfo, String> {
    let mut cmd = create_ffprobe_command()?;
    cmd.args([
        "-v",
        "quiet",
        "-print_format",
        "json",
        "-show_format",
        "-show_streams",
        file_path,
    ]);
    let output = cmd.output().await.map_err(|e| e.to_string())?;

    if !output.status.success() {
        return Err("FFprobe执行失败".to_string());
    }

    let json_str = String::from_utf8(output.stdout).map_err(|e| e.to_string())?;
    let info: serde_json::Value = serde_json::from_str(&json_str).map_err(|e| e.to_string())?;

    // 解析并构建 AudioInfo 结构
    let mut audio_info = AudioInfo {
        filename: file_path.split('/').last().unwrap_or("").to_string(),
        duration: 0.0,
        format: std::path::Path::new(file_path)
            .extension()
            .and_then(|s| s.to_str())
            .unwrap_or("")
            .to_lowercase(),
        size_mb: 0.0,
        audio_codec: String::new(),
        audio_sample_rate: String::new(),
        audio_channels: 0,
        audio_bitrate: String::new(),
    };

    // 填充格式信息
    if let Some(format) = info["format"].as_object() {
        audio_info.duration = format["duration"]
            .as_str()
            .unwrap_or("0")
            .parse()
            .unwrap_or(0.0);
        let size_bytes: u64 = format["size"].as_str().unwrap_or("0").parse().unwrap_or(0);
        audio_info.size_mb = size_bytes as f64 / 1024.0 / 1024.0;
    }

    // 填充流信息
    if let Some(streams) = info["streams"].as_array() {
        for stream in streams {
            if stream["codec_type"].as_str().unwrap_or("") == "audio" {
                audio_info.audio_codec =
                    stream["codec_name"].as_str().unwrap_or("未知").to_string();
                audio_info.audio_sample_rate =
                    stream["sample_rate"].as_str().unwrap_or("0").to_string();
                audio_info.audio_channels = stream["channels"].as_i64().unwrap_or(0);
                if let Some(bit_rate) = stream["bit_rate"].as_str() {
                    let kbps = bit_rate.parse::<i64>().unwrap_or(0) / 1000;
                    audio_info.audio_bitrate = format!("{} kbps", kbps);
                }
                break; // 音频文件通常只有一个音频流
            }
        }
    }

    Ok(audio_info)
}

// 获取图片文件信息
pub async fn get_image_info_internal(file_path: &str) -> Result<ImageInfo, String> {
    let mut cmd = create_ffprobe_command()?;
    cmd.args([
        "-v",
        "quiet",
        "-print_format",
        "json",
        "-show_format",
        "-show_streams",
        file_path,
    ]);
    let output = cmd.output().await.map_err(|e| e.to_string())?;

    if !output.status.success() {
        return Err("FFprobe执行失败".to_string());
    }

    let json_str = String::from_utf8(output.stdout).map_err(|e| e.to_string())?;
    let info: serde_json::Value = serde_json::from_str(&json_str).map_err(|e| e.to_string())?;

    // 解析并构建 ImageInfo 结构
    let mut image_info = ImageInfo {
        filename: file_path.split('/').last().unwrap_or("").to_string(),
        format: std::path::Path::new(file_path)
            .extension()
            .and_then(|s| s.to_str())
            .unwrap_or("")
            .to_lowercase(),
        size_mb: 0.0,
        resolution: String::new(),
        color_space: String::new(),
    };

    // 填充格式信息
    if let Some(format) = info["format"].as_object() {
        let size_bytes: u64 = format["size"].as_str().unwrap_or("0").parse().unwrap_or(0);
        image_info.size_mb = size_bytes as f64 / 1024.0 / 1024.0;
    }

    // 填充流信息
    if let Some(streams) = info["streams"].as_array() {
        for stream in streams {
            if stream["codec_type"].as_str().unwrap_or("") == "video" {
                let width = stream["width"].as_i64().unwrap_or(0);
                let height = stream["height"].as_i64().unwrap_or(0);
                image_info.resolution = format!("{}x{}", width, height);
                image_info.color_space = stream["pix_fmt"].as_str().unwrap_or("未知").to_string();
                break; // 图片文件通常只有一个视频流
            }
        }
    }

    Ok(image_info)
}
