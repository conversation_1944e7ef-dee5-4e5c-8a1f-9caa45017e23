use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
pub async fn adjust_brightness(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    brightness: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始调整亮度: {} -> {}", input_path, output_path);
    println!("亮度调整值: {}", brightness);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("eq=brightness={}", brightness / 100.0), // 转换为-1到1的范围
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("COLOR_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("亮度调整处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("COLOR_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 亮度调整完成！");
    window
        .emit("COLOR_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn adjust_contrast(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    contrast: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始调整对比度: {} -> {}", input_path, output_path);
    println!("对比度调整值: {}", contrast);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("eq=contrast={}", contrast),
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("COLOR_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("对比度调整处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("COLOR_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 对比度调整完成！");
    window
        .emit("COLOR_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn adjust_saturation(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    saturation: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始调整饱和度: {} -> {}", input_path, output_path);
    println!("饱和度调整值: {}", saturation);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("eq=saturation={}", saturation),
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("COLOR_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("饱和度调整处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("COLOR_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 饱和度调整完成！");
    window
        .emit("COLOR_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn adjust_hue(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    hue: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始调整色相: {} -> {}", input_path, output_path);
    println!("色相调整值: {}", hue);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("hue=h={}", hue),
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("COLOR_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("色相调整处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("COLOR_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 色相调整完成！");
    window
        .emit("COLOR_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn adjust_gamma(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    gamma: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始调整伽马值: {} -> {}", input_path, output_path);
    println!("伽马值: {}", gamma);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("eq=gamma={}", gamma),
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("COLOR_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("伽马值调整处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("COLOR_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 伽马值调整完成！");
    window
        .emit("COLOR_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn adjust_white_balance(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    temperature: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始调整白平衡: {} -> {}", input_path, output_path);
    println!("色温: {}K", temperature);

    // 根据色温计算RGB增益
    // 这是一个简化的色温转换，实际应用中可能需要更复杂的算法
    let (r_gain, g_gain, b_gain) = if temperature < 6500.0 {
        // 暖色调（偏黄）
        let factor = (6500.0 - temperature) / 4500.0;
        (1.0 + factor * 0.3, 1.0, 1.0 - factor * 0.3)
    } else {
        // 冷色调（偏蓝）
        let factor = (temperature - 6500.0) / 5500.0;
        (1.0 - factor * 0.3, 1.0, 1.0 + factor * 0.3)
    };

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!(
                "eq=gamma_r={}:gamma_g={}:gamma_b={}",
                r_gain, g_gain, b_gain
            ),
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("COLOR_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("白平衡调整处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("COLOR_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 白平衡调整完成！");
    window
        .emit("COLOR_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}
