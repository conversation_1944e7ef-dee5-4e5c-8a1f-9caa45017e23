use super::{ActionChain, ActionChainInfo, ActionChainManager, ActionData};
use std::sync::Mutex;
use tauri::{AppHandle, State};

// 全局状态管理
pub type ActionChainManagerState = Mutex<Option<ActionChainManager>>;

// 初始化动作链管理器
#[tauri::command]
pub async fn init_action_chain_manager(
    app_handle: AppHandle,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<(), String> {
    let manager = ActionChainManager::new(&app_handle)?;
    let mut state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    *state = Some(manager);
    Ok(())
}

// 保存动作链
#[tauri::command]
pub async fn save_action_chain(
    name: String,
    description: String,
    actions: Vec<ActionData>,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<(), String> {
    let mut state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_mut() {
        let chain = ActionChain {
            name,
            description,
            version: "1.0".to_string(),
            actions,
        };
        manager.save_action_chain(&chain)
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}

// 加载动作链
#[tauri::command]
pub async fn load_action_chain(
    name: String,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<ActionChain, String> {
    let mut state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_mut() {
        manager.load_action_chain(&name)
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}

// 删除动作链
#[tauri::command]
pub async fn delete_action_chain(
    name: String,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<(), String> {
    let mut state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_mut() {
        manager.delete_action_chain(&name)
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}

// 重命名动作链
#[tauri::command]
pub async fn rename_action_chain(
    old_name: String,
    new_name: String,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<(), String> {
    let mut state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_mut() {
        manager.rename_action_chain(&old_name, &new_name)
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}

// 获取动作链列表
#[tauri::command]
pub async fn list_action_chains(
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<Vec<ActionChainInfo>, String> {
    let state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_ref() {
        manager.list_action_chains()
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}

// 更新使用统计
#[tauri::command]
pub async fn update_action_chain_usage(
    name: String,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<(), String> {
    let mut state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_mut() {
        manager.update_usage_stats(&name)
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}

// 导出动作链
#[tauri::command]
pub async fn export_action_chain(
    name: String,
    export_path: String,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<(), String> {
    let state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_ref() {
        manager.export_action_chain(&name, &export_path)
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}

// 导入动作链
#[tauri::command]
pub async fn import_action_chain(
    import_path: String,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<String, String> {
    let mut state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_mut() {
        manager.import_action_chain(&import_path)
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}

// 编辑动作链描述
#[tauri::command]
pub async fn update_action_chain_description(
    name: String,
    description: String,
    manager_state: State<'_, ActionChainManagerState>,
) -> Result<(), String> {
    let mut state = manager_state.lock().map_err(|e| format!("获取状态锁失败: {}", e))?;
    
    if let Some(manager) = state.as_mut() {
        // 先加载动作链
        let mut chain = manager.load_action_chain(&name)?;
        // 更新描述
        chain.description = description;
        // 保存回去
        manager.save_action_chain(&chain)
    } else {
        Err("动作链管理器未初始化".to_string())
    }
}
