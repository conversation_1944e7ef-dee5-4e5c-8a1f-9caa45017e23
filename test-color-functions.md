# 颜色调整功能测试指南

## 已实现的功能

### 1. 调整亮度 (adjust_brightness)
- **参数范围**: -100 到 100
- **默认值**: 0
- **功能**: 调整视频的整体亮度
- **测试建议**: 
  - 使用正值（如 20）增加亮度
  - 使用负值（如 -20）降低亮度
  - 观察视频是否变亮或变暗

### 2. 调整对比度 (adjust_contrast)
- **参数范围**: 0.1 到 3.0
- **默认值**: 1.0
- **功能**: 调整视频的对比度
- **测试建议**:
  - 使用大于1的值（如 1.5）增加对比度
  - 使用小于1的值（如 0.8）降低对比度
  - 观察明暗对比是否增强或减弱

### 3. 调整饱和度 (adjust_saturation)
- **参数范围**: 0.0 到 3.0
- **默认值**: 1.0
- **功能**: 调整视频的颜色饱和度
- **测试建议**:
  - 使用大于1的值（如 2.0）增加饱和度，颜色更鲜艳
  - 使用小于1的值（如 0.5）降低饱和度，颜色更淡
  - 使用0.0完全去除颜色，变成黑白

### 4. 调整色相 (adjust_hue)
- **参数范围**: -180 到 180
- **默认值**: 0
- **功能**: 调整视频的色相
- **测试建议**:
  - 使用正值（如 90）将颜色向绿色方向偏移
  - 使用负值（如 -90）将颜色向红色方向偏移
  - 观察整体色调是否发生变化

### 5. 调整伽马值 (adjust_gamma)
- **参数范围**: 0.1 到 3.0
- **默认值**: 1.0
- **功能**: 调整视频的伽马值，影响中间色调
- **测试建议**:
  - 使用大于1的值（如 1.5）使中间色调更亮
  - 使用小于1的值（如 0.7）使中间色调更暗
  - 观察视频的整体明暗分布

### 6. 白平衡 (adjust_white_balance)
- **参数范围**: 2000 到 12000 (色温K)
- **默认值**: 6500
- **功能**: 调整视频的色温
- **测试建议**:
  - 使用较低值（如 4000）产生暖色调（偏黄）
  - 使用较高值（如 8000）产生冷色调（偏蓝）
  - 观察视频的整体色调是否变暖或变冷

## 测试步骤

1. **启动应用**: 运行 `pnpm tauri dev`
2. **选择视频**: 在右侧上传一个测试视频文件
3. **添加颜色调整动作**: 在左侧"颜色调整"类别中选择任意一个动作
4. **调整参数**: 在右侧动作卡片中调整相应的参数
5. **开始处理**: 点击"开始处理"按钮
6. **观察结果**: 查看输出视频的效果是否符合预期

## 技术实现

### 后端 (Rust)
- 使用 `ffmpeg-sidecar` 库调用 FFmpeg
- 通过 `eq` 滤镜实现亮度、对比度、饱和度、伽马值调整
- 通过 `hue` 滤镜实现色相调整
- 通过 `gamma_r`, `gamma_g`, `gamma_b` 实现白平衡调整
- 支持进度回调，实时显示处理进度

### 前端 (Svelte)
- 参数输入控件支持数值范围限制
- 实时参数验证和更新
- 进度对话框显示处理状态
- 支持链式处理多个颜色调整动作

## 注意事项

1. **处理时间**: 颜色调整需要重新编码视频，处理时间较长
2. **文件大小**: 输出文件可能会比原文件大
3. **质量损失**: 多次处理可能导致质量损失
4. **参数范围**: 请确保参数在指定范围内，避免极端值
5. **预览功能**: 目前没有实时预览功能，需要处理完成后查看结果

## 故障排除

1. **FFmpeg错误**: 确保系统已安装FFmpeg
2. **参数错误**: 检查参数是否在有效范围内
3. **文件权限**: 确保有写入输出目录的权限
4. **内存不足**: 大文件处理可能需要较多内存 