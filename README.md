# VideoIDE - 视频处理工具

一个基于 Svelte 5 + Tauri 2 的桌面视频处理工具，支持链式处理多种视频操作。

## 技术架构

- **前端**: Svelte 5 + TypeScript
- **桌面框架**: Tauri 2
- **样式**: Tailwind CSS + shadcn-svelte
- **包管理**: pnpm
- **视频处理**: ez-ffmpeg (Rust)
- **操作系统**: Windows 10

## 功能特性

### 视频处理动作
- **剪切操作**
  - 截掉头部 - 从指定时间开始保留视频
  - 截掉尾部 - 保留到指定时间
  - 截取片段 - 截取指定时间段

- **颜色调整**
  - 调整亮度 (-100 到 100)
  - 调整对比度 (0.1 到 3.0)
  - 调整饱和度 (0.0 到 3.0)
  - 调整色相 (-180 到 180)
  - 调整伽马值 (0.1 到 3.0)
  - 白平衡 (色温 2000 到 12000)

### 核心功能
- ✅ 支持拖拽选择视频文件
- ✅ 链式处理多个动作
- ✅ 实时进度显示（当前动作进度 + 总体进度）
- ✅ 动作拖拽排序
- ✅ 动作折叠/展开
- ✅ 临时文件自动清理
- ✅ 视频信息显示（时长、编码、分辨率等）

## 新功能说明

### 1. 优化进度处理
- **双重进度显示**: 同时显示当前动作进度和总体进度
- **动作序号显示**: 显示当前处理的动作序号，如"动作(3/6)"
- **实时更新**: 进度条实时更新，提供更好的用户体验

### 2. 多个动作连续处理
- **链式处理**: 多个动作按顺序连续处理
- **临时文件管理**: 中间动作输出临时文件，只有最后一个动作输出最终文件
- **自动清理**: 处理完成后自动清理所有临时文件
- **错误处理**: 处理失败时自动清理已创建的临时文件

## 使用方法

1. **选择视频**: 点击"选择视频文件"或直接拖拽视频文件到指定区域
2. **添加动作**: 从左侧动作列表中选择需要的处理动作
3. **配置参数**: 为每个动作设置相应的参数
4. **调整顺序**: 通过拖拽或上下移动按钮调整动作处理顺序
5. **开始处理**: 点击"开始处理"按钮，系统将按顺序执行所有动作

## 开发

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
pnpm tauri dev
```

### 构建
```bash
pnpm build
```

## 项目结构

```
videoide/
├── src/                    # 前端源码
│   ├── lib/
│   │   ├── components/     # UI组件
│   │   └── utils.ts        # 工具函数
│   └── routes/             # 页面路由
├── src-tauri/              # Rust后端
│   ├── src/
│   │   ├── lib.rs          # 视频处理逻辑
│   │   └── main.rs         # 主入口
│   └── Cargo.toml          # Rust依赖
└── static/                 # 静态资源
```

## 许可证

MIT License
