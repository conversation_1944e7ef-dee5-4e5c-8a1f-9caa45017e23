use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

#[tauri::command]
pub async fn rotate_video(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    angle: i32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始旋转视频: {} -> {}", input_path, output_path);
    println!("旋转角度: {}度", angle);

    // 根据角度选择合适的旋转方法
    let filter = match angle {
        90 => "transpose=1",              // 顺时针90度，交换宽高
        180 => "transpose=1,transpose=1", // 180度，两次90度旋转
        270 => "transpose=2",             // 逆时针90度（270度），交换宽高
        _ => {
            // 其他角度使用rotate滤镜
            &format!("rotate={}*PI/180", angle)
        }
    };

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("TRIM_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    let mut cancelled = false;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频旋转处理已取消");
            kill_ffmpeg_process();
            cancelled = true;
            break;
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("TRIM_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    if cancelled || is_cancelled() {
        return Err("处理已取消".to_string());
    }
    println!("✅ 视频旋转完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn flip_video_horizontal(
    window: tauri::Window,
    input_path: String,
    output_path: String,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始水平翻转视频: {} -> {}", input_path, output_path);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            "hflip",
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("TRIM_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频水平翻转处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("TRIM_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 水平翻转完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn flip_video_vertical(
    window: tauri::Window,
    input_path: String,
    output_path: String,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始垂直翻转视频: {} -> {}", input_path, output_path);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            "vflip",
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("TRIM_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频垂直翻转处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("TRIM_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 垂直翻转完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn scale_video(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    scale: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始缩放视频: {} -> {}", input_path, output_path);
    println!("缩放比例: {}", scale);

    let scale_filter = format!("scale=iw*{}:ih*{}", scale, scale);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &scale_filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("TRIM_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    let mut cancelled = false;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频缩放处理已取消");
            kill_ffmpeg_process();
            cancelled = true;
            break;
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("TRIM_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    if cancelled || is_cancelled() {
        return Err("处理已取消".to_string());
    }
    println!("✅ 视频缩放完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}
