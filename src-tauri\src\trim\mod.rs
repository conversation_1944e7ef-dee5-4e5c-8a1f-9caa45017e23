use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
pub async fn trim_video_start(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    start_duration: f32,
) -> Result<(), String> {
    println!("trim_video_start called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  start_duration: {}", start_duration);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("跳过前 {:.2} 秒", start_duration);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-ss",
            &start_duration.to_string(),
            "-i",
            &input_path,
            "-c",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("TRIM_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("TRIM_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn trim_video_end(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    end_time: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频总时长
    let video_info = get_video_info_internal(&input_path).await?;
    let total_seconds = video_info.duration;
    let trim_duration = total_seconds - end_time as f64;
    if trim_duration <= 0.0 {
        return Err("截取时长无效".to_string());
    }

    let total_frames = (trim_duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("截取前 {:.2} 秒", trim_duration);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-t",
            &trim_duration.to_string(),
            "-c",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("TRIM_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("TRIM_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn trim_video_segment(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    start_time: f32,
    end_time: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频总时长
    let video_info = get_video_info_internal(&input_path).await?;
    let total_seconds = video_info.duration;
    if start_time < 0.0 || end_time <= start_time || end_time > total_seconds as f32 {
        return Err(format!(
            "参数错误: start_time={}, end_time={}, 视频总时长={}",
            start_time, end_time, total_seconds
        ));
    }
    let duration = end_time - start_time;

    let total_frames = (duration as f64 * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!(
        "截取片段: {:.2}s 到 {:.2}s (时长: {:.2}s)",
        start_time, end_time, duration
    );

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-ss",
            &start_time.to_string(),
            "-i",
            &input_path,
            "-t",
            &duration.to_string(),
            "-c",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("TRIM_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("TRIM_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn crop_video(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    x: i32,
    y: i32,
    width: i32,
    height: i32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 参数验证
    if x < 0 || y < 0 || width <= 0 || height <= 0 {
        return Err("裁剪参数无效：坐标和尺寸必须为正数".to_string());
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    println!("开始裁剪视频: {} -> {}", input_path, output_path);
    println!(
        "裁剪区域: x={}, y={}, width={}, height={}",
        x, y, width, height
    );

    // 构造裁剪滤镜
    let crop_filter = format!("crop={}:{}:{}:{}", width, height, x, y);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &crop_filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window.emit("CROP_PROGRESS", Payload { pct: 0.0 }).unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频裁剪处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("CROP_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频裁剪完成！");
    window
        .emit("CROP_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 合并多个视频文件
#[tauri::command]
pub async fn concat_videos(
    window: tauri::Window,
    input_paths: Vec<String>,
    output_path: String,
) -> Result<(), String> {
    println!("concat_videos called with:");
    println!("  input_paths: {:?}", input_paths);
    println!("  output_path: {}", output_path);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    for input_path in &input_paths {
        if !Path::new(input_path).exists() {
            return Err(format!("输入视频文件不存在: {}", input_path));
        }
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 创建临时文件列表
    let temp_list_file = std::env::temp_dir().join("concat_list.txt");
    let mut file_list_content = String::new();

    for input_path in &input_paths {
        file_list_content.push_str(&format!("file '{}'\n", input_path.replace('\\', "/")));
    }

    std::fs::write(&temp_list_file, &file_list_content).map_err(|e| e.to_string())?;

    println!("创建文件列表: {:?}", temp_list_file);
    println!("文件列表内容:\n{}", file_list_content);

    // 使用FFmpeg合并视频
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            temp_list_file.to_str().unwrap(),
            "-c",
            "copy",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg合并处理失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 等待处理完成并监控进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg合并处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频合并已取消");
            kill_ffmpeg_process();
            let _ = std::fs::remove_file(&temp_list_file);
            return Err("处理已取消".to_string());
        }

        let pct = (progress.frame as f64 * 100.0 / 1000.0).min(100.0) as f32;
        if pct - last_pct >= 5.0 {
            window.emit("TRIM_PROGRESS", Payload { pct }).unwrap();
            last_pct = pct;
        }
    }

    // 等待处理完成
    let status = ffmpeg.wait().map_err(|e| {
        println!("[FFmpeg wait error] {e:?}");
        format!("FFmpeg合并处理等待失败: {e}")
    })?;

    // 清理临时文件列表
    let _ = std::fs::remove_file(&temp_list_file);

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    if !status.success() {
        return Err(format!("视频合并失败: {:?}", status.code()));
    }

    println!("✅ 视频合并完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 内部函数：截取多个片段并合并为一个视频
async fn trim_and_concat_segments_internal(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    segments: Vec<(f32, f32)>, // (start_time, end_time) 数组
) -> Result<(), String> {
    println!("trim_and_concat_segments_internal called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  segments: {:?}", segments);

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入视频文件不存在".to_string());
    }

    if segments.is_empty() {
        return Err("请先添加至少一个视频片段".to_string());
    }

    // 处理重叠片段：按开始时间排序，然后合并重叠部分
    let mut processed_segments = segments.clone();
    processed_segments.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());

    // 检测并处理重叠
    let mut merged_segments = Vec::new();
    for (start, end) in processed_segments {
        if merged_segments.is_empty() {
            merged_segments.push((start, end));
        } else {
            let last_idx = merged_segments.len() - 1;
            let (last_start, last_end) = merged_segments[last_idx];

            if start <= last_end {
                // 有重叠，合并片段
                println!(
                    "检测到重叠片段: [{:.2}, {:.2}] 与 [{:.2}, {:.2}]",
                    last_start, last_end, start, end
                );
                merged_segments[last_idx] = (last_start, end.max(last_end));
                println!("合并为: [{:.2}, {:.2}]", last_start, end.max(last_end));
            } else {
                // 无重叠，添加新片段
                merged_segments.push((start, end));
            }
        }
    }

    println!(
        "原始片段数: {}, 处理后片段数: {}",
        segments.len(),
        merged_segments.len()
    );
    let segments = merged_segments;

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 创建临时目录用于存储片段文件
    let temp_dir = std::env::temp_dir().join(format!("trim_segments_{}", std::process::id()));
    std::fs::create_dir_all(&temp_dir).map_err(|e| e.to_string())?;

    let mut temp_segment_files: Vec<String> = Vec::new();
    let total_segments = segments.len();

    // 获取原视频信息以确保一致的编码参数
    let video_info = get_video_info_internal(&input_path).await?;
    println!(
        "原视频信息: 分辨率: {}, fps: {}, 编码: {}",
        video_info.resolution, video_info.fps, video_info.video_codec
    );

    // 第一阶段：截取所有片段 (0-80% 进度)
    for (i, (start_time, end_time)) in segments.iter().enumerate() {
        if is_cancelled() {
            println!("处理已取消");
            // 清理临时文件
            for temp_file in &temp_segment_files {
                let _ = std::fs::remove_file(temp_file);
            }
            let _ = std::fs::remove_dir_all(&temp_dir);
            return Err("处理已取消".to_string());
        }

        let temp_segment_path = temp_dir.join(format!("segment_{}.mp4", i + 1));
        let temp_segment_path_str = temp_segment_path.to_str().unwrap().to_string();

        println!(
            "处理片段 {}/{}: {:.2}s - {:.2}s",
            i + 1,
            total_segments,
            start_time,
            end_time
        );

        // 计算当前片段的进度范围 (每个片段占用 80% / total_segments 的进度)
        let segment_progress_start = (i as f32 / total_segments as f32) * 80.0;
        let segment_progress_end = ((i + 1) as f32 / total_segments as f32) * 80.0;

        // 发送开始处理当前片段的进度
        window
            .emit(
                "TRIM_PROGRESS",
                Payload {
                    pct: segment_progress_start,
                },
            )
            .unwrap();

        // 截取片段 - 使用精确的时间戳和重新编码确保一致性
        let duration = end_time - start_time;

        // 解析原视频分辨率
        let (width, height) = if let Ok(resolution_parts) = video_info
            .resolution
            .split('x')
            .collect::<Vec<&str>>()
            .try_into()
        {
            let parts: [&str; 2] = resolution_parts;
            (
                parts[0].parse::<i32>().unwrap_or(1920),
                parts[1].parse::<i32>().unwrap_or(1080),
            )
        } else {
            (1920, 1080) // 默认分辨率
        };

        let mut ffmpeg = config::create_ffmpeg_command()?
            .args([
                "-ss",
                &start_time.to_string(),
                "-i",
                &input_path,
                "-t",
                &duration.to_string(),
                "-c:v",
                "libx264",
                "-c:a",
                "aac",
                "-s",
                &format!("{}x{}", width, height), // 保持原视频分辨率
                "-r",
                &video_info.fps, // 保持原视频帧率
                "-crf",
                "23", // 高质量编码
                "-preset",
                "medium",
                "-avoid_negative_ts",
                "make_zero", // 确保时间戳从0开始
                "-fflags",
                "+genpts", // 重新生成时间戳
                "-y",
                &temp_segment_path_str,
            ])
            .spawn()
            .map_err(|e| {
                println!("[FFmpeg spawn error] {e:?}");
                format!("FFmpeg截取片段失败: {e}")
            })?;

        // 标记ffmpeg进程开始运行
        mark_ffmpeg_running();

        // 等待片段截取完成
        let status = ffmpeg.wait().map_err(|e| {
            println!("[FFmpeg wait error] {e:?}");
            format!("FFmpeg截取片段等待失败: {e}")
        })?;

        if !status.success() {
            // 清理临时文件
            for temp_file in &temp_segment_files {
                let _ = std::fs::remove_file(temp_file);
            }
            let _ = std::fs::remove_dir_all(&temp_dir);
            return Err(format!("片段 {} 截取失败", i + 1));
        }

        temp_segment_files.push(temp_segment_path_str);

        // 发送当前片段完成的进度
        window
            .emit(
                "TRIM_PROGRESS",
                Payload {
                    pct: segment_progress_end,
                },
            )
            .unwrap();
    }

    // 第二阶段：合并所有片段 (80-100% 进度)
    println!("开始合并 {} 个片段...", temp_segment_files.len());

    window.emit("TRIM_PROGRESS", Payload { pct: 80.0 }).unwrap();

    if temp_segment_files.len() == 1 {
        // 只有一个片段，直接移动文件
        std::fs::rename(&temp_segment_files[0], &output_path)
            .map_err(|e| format!("移动文件失败: {}", e))?;
    } else {
        // 多个片段，需要合并
        let temp_list_file = temp_dir.join("concat_list.txt");
        let mut file_list_content = String::new();

        for temp_file in &temp_segment_files {
            file_list_content.push_str(&format!("file '{}'\n", temp_file.replace('\\', "/")));
        }

        std::fs::write(&temp_list_file, &file_list_content).map_err(|e| e.to_string())?;

        // 使用FFmpeg合并视频 - 由于片段已经统一编码，可以安全使用copy
        let mut ffmpeg = config::create_ffmpeg_command()?
            .args([
                "-y",
                "-f",
                "concat",
                "-safe",
                "0",
                "-i",
                temp_list_file.to_str().unwrap(),
                "-c",
                "copy",
                "-avoid_negative_ts",
                "make_zero",
                &output_path,
            ])
            .spawn()
            .map_err(|e| {
                println!("[FFmpeg spawn error] {e:?}");
                format!("FFmpeg合并处理失败: {e}")
            })?;

        // 标记ffmpeg进程开始运行
        mark_ffmpeg_running();

        // 监控合并进度 (80-100%)
        let mut last_pct = 80.0f32;
        for progress in ffmpeg
            .iter()
            .map_err(|e| format!("FFmpeg合并处理失败: {}", e))?
            .filter_progress()
        {
            if is_cancelled() {
                println!("视频合并已取消");
                kill_ffmpeg_process();
                // 清理临时文件
                for temp_file in &temp_segment_files {
                    let _ = std::fs::remove_file(temp_file);
                }
                let _ = std::fs::remove_dir_all(&temp_dir);
                return Err("处理已取消".to_string());
            }

            let pct = 80.0 + (progress.frame as f64 * 20.0 / 1000.0).min(20.0) as f32;
            if pct - last_pct >= 2.0 {
                window.emit("TRIM_PROGRESS", Payload { pct }).unwrap();
                last_pct = pct;
            }
        }

        // 等待合并完成
        let status = ffmpeg.wait().map_err(|e| {
            println!("[FFmpeg wait error] {e:?}");
            format!("FFmpeg合并处理等待失败: {e}")
        })?;

        if !status.success() {
            // 清理临时文件
            for temp_file in &temp_segment_files {
                let _ = std::fs::remove_file(temp_file);
            }
            let _ = std::fs::remove_dir_all(&temp_dir);
            return Err(format!("视频合并失败: {:?}", status.code()));
        }
    }

    // 清理临时文件和目录
    for temp_file in &temp_segment_files {
        let _ = std::fs::remove_file(temp_file);
    }
    let _ = std::fs::remove_dir_all(&temp_dir);

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 多片段截取合并完成！");
    window
        .emit("TRIM_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 截取多个片段并合并为一个视频
#[tauri::command]
pub async fn trim_and_concat_segments(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    segments: Vec<(f32, f32)>, // (start_time, end_time) 数组
) -> Result<(), String> {
    println!("trim_and_concat_segments called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  segments: {:?}", segments);

    // 重置取消标志
    reset_cancelled();

    // 调用内部函数
    trim_and_concat_segments_internal(window, input_path, output_path, segments).await
}

// 剔除多个片段并合并剩余部分为一个视频
#[tauri::command]
pub async fn exclude_and_concat_segments(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    segments: Vec<(f32, f32)>, // (start_time, end_time) 数组 - 要剔除的片段
) -> Result<(), String> {
    println!("exclude_and_concat_segments called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  segments to exclude: {:?}", segments);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入视频文件不存在".to_string());
    }

    if segments.is_empty() {
        return Err("请先添加至少一个要剔除的视频片段".to_string());
    }

    // 获取视频总时长
    let video_info = get_video_info_internal(&input_path).await?;
    let total_duration = video_info.duration as f32;
    println!("视频总时长: {:.2}秒", total_duration);

    // 处理要剔除的片段：按开始时间排序，然后合并重叠部分
    let mut exclude_segments = segments.clone();
    exclude_segments.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());

    // 检测并处理重叠
    let mut merged_exclude_segments = Vec::new();
    for (start, end) in exclude_segments {
        if merged_exclude_segments.is_empty() {
            merged_exclude_segments.push((start, end));
        } else {
            let last_idx = merged_exclude_segments.len() - 1;
            let (last_start, last_end) = merged_exclude_segments[last_idx];

            if start <= last_end {
                // 有重叠，合并片段
                println!(
                    "检测到重叠的剔除片段: [{:.2}, {:.2}] 与 [{:.2}, {:.2}]",
                    last_start, last_end, start, end
                );
                merged_exclude_segments[last_idx] = (last_start, end.max(last_end));
                println!("合并为: [{:.2}, {:.2}]", last_start, end.max(last_end));
            } else {
                // 无重叠，添加新片段
                merged_exclude_segments.push((start, end));
            }
        }
    }

    println!(
        "原始剔除片段数: {}, 处理后剔除片段数: {}",
        segments.len(),
        merged_exclude_segments.len()
    );

    // 计算要保留的片段（剔除片段之外的部分）
    let mut keep_segments = Vec::new();
    let mut current_time = 0.0;

    for (exclude_start, exclude_end) in &merged_exclude_segments {
        // 如果当前时间小于剔除片段的开始时间，添加这部分为保留片段
        if current_time < *exclude_start {
            keep_segments.push((current_time, *exclude_start));
            println!("保留片段: [{:.2}, {:.2}]", current_time, *exclude_start);
        }
        // 更新当前时间为剔除片段的结束时间
        current_time = *exclude_end;
    }

    // 如果最后一个剔除片段结束后还有视频内容，添加为保留片段
    if current_time < total_duration {
        keep_segments.push((current_time, total_duration));
        println!("保留片段: [{:.2}, {:.2}]", current_time, total_duration);
    }

    if keep_segments.is_empty() {
        return Err("剔除指定片段后没有剩余内容".to_string());
    }

    println!("计算出 {} 个保留片段", keep_segments.len());

    // 使用现有的trim_and_concat_segments逻辑来处理保留的片段
    // 但是我们需要直接调用内部实现，而不是通过tauri命令
    trim_and_concat_segments_internal(window, input_path, output_path, keep_segments).await
}
