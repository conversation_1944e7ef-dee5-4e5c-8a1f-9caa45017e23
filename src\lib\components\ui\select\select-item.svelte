<script lang="ts">
  import { cn } from "$lib/utils";

  interface Props {
    value: string;
    class?: string;
    children?: any;
  }

  let {
    value,
    class: className,
    children,
    ...props
  }: Props = $props();
</script>

<option
  {value}
  class={cn(
    "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
    className
  )}
  {...props}
>
  {@render children?.()}
</option>
