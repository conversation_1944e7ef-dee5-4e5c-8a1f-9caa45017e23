{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[18251603632334587267, "build_script_build", false, 8784092642136989538], [10755362358622467486, "build_script_build", false, 13759496898265183797], [3834743577069889284, "build_script_build", false, 12423692095046048082], [422130612855741759, "build_script_build", false, 1074176877319558024], [17218623086136245857, "build_script_build", false, 12387895125967380034]], "local": [{"RerunIfChanged": {"output": "debug\\build\\VideoIDE-413f6ab3efeddb57\\output", "paths": ["tauri.conf.json", "capabilities", "lib\\ffmpeg\\ffmpeg.exe", "lib\\ffmpeg\\ffprobe.exe", "lib\\mpv\\libmpv-2.dll"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}