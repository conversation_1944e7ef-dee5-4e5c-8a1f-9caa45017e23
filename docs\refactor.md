# 项目重构说明

## 前端重构
### 当前前端问题
1. 所有动作现在写在同一个文件 `action-list.svelte`, 修改, 添加新得动作不方便
2. 很多地方判断使用了动作中文名称 `裁剪画面`, 应该使用动作id `crop` , 文件 `action-card.svelte`
3. 有些函数使用非常长的函数, 对每个动作使用switch case等, 不方便维护
4. 一些地方使用字符串常量, 比如事件处理等 `VIDEOTOIMAGE_PROGRESS`, 应该有统一的地方定义枚举等数据结构, 文件 `processing-dialog.svelte`
5. 目前所有文字都是直接写在页面, 不方便支持多语言 i18n

### 重构目标
1. 规范代码结构, 尽量模块分离, 责任分离, 方便后续维护添加新得动作, 类别, 修改动作功能等, 避免编辑多个文件
2. 避免长函数分支 switch-case 等
3. 所有判断动作的地方统一使用`action.id` 如 `crop`, 不使用`action.name` 如 `裁剪画面`
4. 类似常量使用枚举管理, 如事件等
5. 支持 i18n, 所有需要文字地方都同意处理, 支持多语言 (如有svelte相关框架可以使用)
6. 语言资源文件独立定义, 如`chinese.json`, `english.json`
7. 提供切换语言功能, 初始语言可根据系统语言检测决定
8. 提供一些未来可升级自定义的地方, 比如一个初始化地方, 可以以后加入权限验证等功能

### 重构设计
- 建立统一Action, Category 类
```
Category 
fields - id, name, description

Action
fields - id, name, description, categoryId, inputTypes, outputTypes, params(改动作需要的输入参数定义)
methods - validate (用来执行处理前验证参数是否满足要求)

目前Action定义在几个地方`action-card.svelte`, `action-list.svelte`, 需要放在一起定义

```

- 将动作按类别分模块定义(匹配后台), 例如将`裁剪`类别的所有动作定义在一个文件, 模块名可参考后台rust模块名

- 使用常数, Enum 等方式统一定义字面量, 不直接使用字符串在代码中, 尤其是逻辑判断等地方, 比如ActionEvent, 用来定义所有的动作进度事件

- 使用一个模块动态在软件初始化组装, 加载所有类别和动作(因后续可能会有权限系统, 不同全限可用动作不一样, 比如免费用户能用的动作比付费用户少)

- 提供一个软件初始化的统一定义地方

- 消除重复的代码逻辑, 在一些参数展现, 文件验证等地方, 提取公用函数

- 提取一些公共函数放在公共模块

- 将所有展示字符串使用i18n方式替换, 比如替换为id, 初始化时加载语言文件

### 重构执行步骤
考虑到此次重构规模大, 涉及范围广, 重构分为几个步骤, 每步相对独立, 成功后通过基本测试提交代码才执行下一步

1. 定义Category, 比如ActionEvent等数据结构, 并更新相关代码

2. 定义Action, 将动作按类别分开放置到各个模块, 然后由一个地方初始化时组装所有的可用动作, 含统一动作的参数展现, 验证逻辑处理, 减少重复代码

3. 其他非i18n相关的重构, 比如公共函数提取, 公共模块使用等

4. i18n, 语言文件独立定义, 并更新相关代码

### Action类和`page.svelte` 进一步重构
问题: 
现在`page.svelte` 负责所有动作的提交参数验证获取, 以及实际调用rust后端服务的功能, 使用大量的if ... else
不方便维护,而且还在使用`action.name` 

解决: 
1. 更新Action类
- 添加 `invokeCommand` 字段, 用来指定该动作对应的后端服务, 比如`截掉尾部`的后端服务为`trim_video_end`, 这样可以根据执行动作直接获取服务无需使用if...else判断
- 添加 `buildParams` 函数字段, 用来动态构建需要传给后端的参数, `processVideo` 函数用来负责将页面上用户输入参数发送到
该函数, 并经过逻辑处理, 默认值使用等, 获取最终参数列表, 发往后端

2. 清理`page.svelte` 的 庞大 `processVideo` 函数, 取消长if...else , 使用以上新得 Action 精简代码, 并将参数处理逻辑全部移到相应的动作定义中, 以后所有动作相关都只会定义在动作本身, 其他代码是不需要改动的