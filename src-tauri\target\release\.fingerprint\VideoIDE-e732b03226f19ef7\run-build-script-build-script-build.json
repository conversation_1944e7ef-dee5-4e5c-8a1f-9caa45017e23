{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[18251603632334587267, "build_script_build", false, 12420866281827137641], [10755362358622467486, "build_script_build", false, 7021458208738467342], [3834743577069889284, "build_script_build", false, 7051237165771637370], [422130612855741759, "build_script_build", false, 10767752697329148021], [17218623086136245857, "build_script_build", false, 7022038148538142596]], "local": [{"RerunIfChanged": {"output": "release\\build\\VideoIDE-e732b03226f19ef7\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}