<script lang="ts">
  import { cn } from "$lib/utils";

  interface Tab {
    id: string;
    label: string;
    disabled?: boolean;
  }

  interface Props {
    tabs: Tab[];
    activeTab: string;
    onTabChange: (tabId: string) => void;
    class?: string;
  }

  let { tabs, activeTab, onTabChange, class: className = "" }: Props = $props();
</script>

<div class={cn("w-full", className)}>
  <div class="border-b border-border">
    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
      {#each tabs as tab}
        <button
          class={cn(
            "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors",
            activeTab === tab.id
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground hover:border-border",
            tab.disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={tab.disabled}
          onclick={() => !tab.disabled && onTabChange(tab.id)}
        >
          {tab.label}
        </button>
      {/each}
    </nav>
  </div>
</div>
