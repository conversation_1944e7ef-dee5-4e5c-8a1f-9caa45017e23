<script lang="ts">
  interface Props {
    isOpen: boolean;
    title?: string;
    message: string;
    onClose: () => void;
  }

  let { isOpen = false, title = "提示", message, onClose }: Props = $props();
</script>

{#if isOpen}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div
      class="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full mx-4 border border-gray-200 dark:border-gray-700"
    >
      <div class="p-6">
        <div class="flex items-center space-x-3 mb-4">
          <div
            class="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-gray-600 dark:text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {title}
          </h3>
        </div>

        <p class="text-gray-600 dark:text-gray-300 mb-6">{message}</p>

        <div class="flex justify-end">
          <button
            class="px-4 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 rounded-md hover:bg-gray-800 dark:hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            onclick={onClose}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
