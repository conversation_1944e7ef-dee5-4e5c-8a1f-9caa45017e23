// 文件操作工具函数
import { open } from '@tauri-apps/plugin-dialog';
import { invoke } from '@tauri-apps/api/core';
import { ImageFormats, VideoFormats, AudioFormats } from '../constants';

/**
 * 文件类型过滤器
 */
export interface FileFilter {
  name: string;
  extensions: string[];
}

/**
 * 视频文件过滤器
 */
export const videoFileFilter: FileFilter = {
  name: '视频文件',
  extensions: Object.values(VideoFormats)
};

/**
 * 音频文件过滤器
 */
export const audioFileFilter: FileFilter = {
  name: '音频文件',
  extensions: Object.values(AudioFormats)
};

/**
 * 图片文件过滤器
 */
export const imageFileFilter: FileFilter = {
  name: '图片文件',
  extensions: Object.values(ImageFormats)
};

/**
 * 所有媒体文件过滤器
 */
export const mediaFileFilter: FileFilter = {
  name: '媒体文件',
  extensions: [
    ...Object.values(VideoFormats),
    ...Object.values(AudioFormats),
    ...Object.values(ImageFormats)
  ]
};

/**
 * 选择文件
 * @param filters 文件类型过滤器
 * @param multiple 是否允许多选
 * @returns 选择的文件路径
 */
export async function selectFile(
  filters: FileFilter[] = [mediaFileFilter],
  multiple: boolean = false
): Promise<string | string[] | null> {
  try {
    const selected = await open({
      multiple,
      filters
    });
    
    return selected;
  } catch (error) {
    console.error('File selection error:', error);
    return null;
  }
}

/**
 * 选择视频文件
 * @returns 选择的视频文件路径
 */
export async function selectVideoFile(): Promise<string | null> {
  const result = await selectFile([videoFileFilter]);
  return typeof result === 'string' ? result : null;
}

/**
 * 选择音频文件
 * @returns 选择的音频文件路径
 */
export async function selectAudioFile(): Promise<string | null> {
  const result = await selectFile([audioFileFilter]);
  return typeof result === 'string' ? result : null;
}

/**
 * 选择图片文件
 * @returns 选择的图片文件路径
 */
export async function selectImageFile(): Promise<string | null> {
  const result = await selectFile([imageFileFilter]);
  return typeof result === 'string' ? result : null;
}

/**
 * 获取文件信息
 * @param filePath 文件路径
 * @returns 文件信息
 */
export async function getFileInfo(filePath: string): Promise<any> {
  try {
    return await invoke('get_file_info', { path: filePath });
  } catch (error) {
    console.error('Failed to get file info:', error);
    return null;
  }
}

/**
 * 检查文件是否存在
 * @param filePath 文件路径
 * @returns 文件是否存在
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    return await invoke('file_exists', { path: filePath });
  } catch (error) {
    console.error('Failed to check file existence:', error);
    return false;
  }
}

/**
 * 获取文件扩展名
 * @param filePath 文件路径
 * @returns 文件扩展名（小写，不含点）
 */
export function getFileExtension(filePath: string): string {
  return filePath.split('.').pop()?.toLowerCase() || '';
}

/**
 * 检查文件类型
 * @param filePath 文件路径
 * @param extensions 扩展名列表
 * @returns 是否匹配扩展名
 */
export function checkFileType(filePath: string, extensions: string[]): boolean {
  const ext = getFileExtension(filePath);
  return extensions.includes(ext);
}

/**
 * 检查是否为视频文件
 * @param filePath 文件路径
 * @returns 是否为视频文件
 */
export function isVideoFile(filePath: string): boolean {
  return checkFileType(filePath, Object.values(VideoFormats));
}

/**
 * 检查是否为音频文件
 * @param filePath 文件路径
 * @returns 是否为音频文件
 */
export function isAudioFile(filePath: string): boolean {
  return checkFileType(filePath, Object.values(AudioFormats));
}

/**
 * 检查是否为图片文件
 * @param filePath 文件路径
 * @returns 是否为图片文件
 */
export function isImageFile(filePath: string): boolean {
  return checkFileType(filePath, Object.values(ImageFormats));
}
