use rand::Rng;
use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// 视频生图功能
#[tauri::command]
pub async fn video_to_images(
    window: tauri::Window,
    input_path: String,
    count: u32,
    format: String,
    method: String,
    quality: Option<u8>,
    width: Option<i32>,
    height: Option<i32>,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 获取视频信息
    let video_info = get_video_info_internal(&input_path).await?;

    // 创建输出目录
    let input_path_obj = Path::new(&input_path);
    let parent_dir = input_path_obj.parent().unwrap();
    let file_stem = input_path_obj.file_stem().unwrap().to_str().unwrap();
    let output_dir = parent_dir.join(format!("{}_images", file_stem));

    std::fs::create_dir_all(&output_dir).map_err(|e| e.to_string())?;

    println!("开始视频生图: {} -> {:?}", input_path, output_dir);
    println!(
        "参数: count={}, format={}, method={}",
        count, format, method
    );

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("VIDEOTOIMAGE_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    let result = match method.as_str() {
        "uniform" => {
            extract_frames_uniform(
                &window,
                &input_path,
                &output_dir,
                count,
                &format,
                quality,
                width,
                height,
                video_info.duration as f32,
            )
            .await
        }
        "random" => {
            extract_frames_random(
                &window,
                &input_path,
                &output_dir,
                count,
                &format,
                quality,
                width,
                height,
                video_info.duration as f32,
            )
            .await
        }
        _ => Err("不支持的截取方式".to_string()),
    };

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    // 发送完成事件
    if result.is_ok() {
        window
            .emit("VIDEOTOIMAGE_PROGRESS", Payload { pct: 100.0 })
            .unwrap();
    }

    result
}

// 均匀截取帧
async fn extract_frames_uniform(
    window: &tauri::Window,
    input_path: &str,
    output_dir: &Path,
    count: u32,
    format: &str,
    quality: Option<u8>,
    width: Option<i32>,
    height: Option<i32>,
    duration: f32,
) -> Result<(), String> {
    // 计算时间间隔，避开开头和结尾各5%的时间
    let start_offset = duration * 0.05;
    let end_offset = duration * 0.95;
    let usable_duration = end_offset - start_offset;
    let interval = usable_duration / (count as f32);

    for i in 0..count {
        if is_cancelled() {
            println!("视频生图处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let timestamp = start_offset + (i as f32) * interval;
        let output_file = output_dir.join(format!("{:03}.{}", i + 1, format));

        extract_single_frame(input_path, &output_file, timestamp, quality, width, height).await?;

        // 更新进度
        let progress = ((i + 1) as f32 / count as f32) * 100.0;
        window
            .emit("VIDEOTOIMAGE_PROGRESS", Payload { pct: progress })
            .unwrap();
    }

    Ok(())
}

// 随机截取帧
async fn extract_frames_random(
    window: &tauri::Window,
    input_path: &str,
    output_dir: &Path,
    count: u32,
    format: &str,
    quality: Option<u8>,
    width: Option<i32>,
    height: Option<i32>,
    duration: f32,
) -> Result<(), String> {
    // 生成随机时间点，避开开头和结尾各5%的时间
    let start_offset = duration * 0.05;
    let end_offset = duration * 0.95;
    let usable_duration = end_offset - start_offset;

    // 在函数开始时生成所有随机时间点，避免在async上下文中使用thread_rng
    let timestamps = generate_random_timestamps(count, start_offset, usable_duration);

    for (i, timestamp) in timestamps.iter().enumerate() {
        if is_cancelled() {
            println!("视频生图处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let output_file = output_dir.join(format!("{:03}.{}", i + 1, format));

        extract_single_frame(input_path, &output_file, *timestamp, quality, width, height).await?;

        // 更新进度
        let progress = ((i + 1) as f32 / count as f32) * 100.0;
        window
            .emit("VIDEOTOIMAGE_PROGRESS", Payload { pct: progress })
            .unwrap();
    }

    Ok(())
}

// 提取单帧
async fn extract_single_frame(
    input_path: &str,
    output_path: &Path,
    timestamp: f32,
    quality: Option<u8>,
    width: Option<i32>,
    height: Option<i32>,
) -> Result<(), String> {
    let mut args = vec![
        "-ss".to_string(),
        timestamp.to_string(),
        "-i".to_string(),
        input_path.to_string(),
        "-vframes".to_string(),
        "1".to_string(),
        "-avoid_negative_ts".to_string(),
        "make_zero".to_string(),
    ];

    // 添加像素格式以确保兼容性和质量
    args.extend_from_slice(&["-pix_fmt".to_string(), "yuv420p".to_string()]);

    // 添加视频滤镜
    if let Some(w) = width {
        // 只指定宽度，高度自动按比例调整
        args.extend_from_slice(&["-vf".to_string(), format!("scale={}:-1", w)]);
    } else if let (Some(w), Some(h)) = (width, height) {
        // 同时指定宽度和高度
        args.extend_from_slice(&["-vf".to_string(), format!("scale={}:{}", w, h)]);
    }

    // 根据格式设置质量参数
    match output_path.extension().and_then(|s| s.to_str()) {
        Some("jpg") | Some("jpeg") => {
            // 使用更好的JPEG编码器和质量设置
            args.extend_from_slice(&["-c:v".to_string(), "mjpeg".to_string()]);
            if let Some(q) = quality {
                // 将1-100的质量值转换为FFmpeg的-q:v参数（2-31，数值越小质量越高）
                let ffmpeg_quality = ((100 - q) as f32 * 29.0 / 100.0 + 2.0) as u8;
                args.extend_from_slice(&["-q:v".to_string(), ffmpeg_quality.to_string()]);
            } else {
                // 默认高质量
                args.extend_from_slice(&["-q:v".to_string(), "2".to_string()]);
            }
        }
        Some("png") => {
            // PNG使用无损压缩，设置压缩级别（0-9，9为最高压缩）
            args.extend_from_slice(&["-c:v".to_string(), "png".to_string()]);
            if let Some(q) = quality {
                // 质量越高，压缩级别越低（文件越大但质量越好）
                let compression = ((100 - q) / 10).min(9);
                args.extend_from_slice(&[
                    "-compression_level".to_string(),
                    compression.to_string(),
                ]);
            } else {
                // 默认中等压缩
                args.extend_from_slice(&["-compression_level".to_string(), "6".to_string()]);
            }
        }
        Some("webp") => {
            // WebP使用更好的编码器
            args.extend_from_slice(&["-c:v".to_string(), "libwebp".to_string()]);
            if let Some(q) = quality {
                // WebP质量参数直接使用1-100
                args.extend_from_slice(&["-quality".to_string(), q.to_string()]);
            } else {
                // 默认高质量
                args.extend_from_slice(&["-quality".to_string(), "90".to_string()]);
            }
        }
        Some("bmp") => {
            // BMP使用无损格式，不需要质量参数
            args.extend_from_slice(&["-c:v".to_string(), "bmp".to_string()]);
            // BMP是无损格式，不需要质量设置
        }
        _ => {}
    }

    args.extend_from_slice(&["-y".to_string(), output_path.to_str().unwrap().to_string()]);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args(&args)
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    let status = ffmpeg.wait().map_err(|e| {
        println!("[FFmpeg wait error] {e:?}");
        format!("FFmpeg等待失败: {e}")
    })?;

    if !status.success() {
        return Err("FFmpeg处理失败".to_string());
    }

    Ok(())
}

// 视频转动图功能（支持GIF和WebP）
#[tauri::command]
pub async fn video_to_gif(
    window: tauri::Window,
    input_path: String,
    format: String,
    start_time: f32,
    duration: f32,
    fps: f32,
    width: Option<i32>,
    height: Option<i32>,
    quality: u8,
    loop_count: i32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 获取视频信息
    let video_info = get_video_info_internal(&input_path).await?;

    // 验证时间参数
    if start_time < 0.0 || start_time >= video_info.duration as f32 {
        return Err("开始时间超出视频范围".to_string());
    }

    let end_time = start_time + duration;
    if end_time > video_info.duration as f32 {
        return Err("结束时间超出视频范围".to_string());
    }

    // 创建输出文件路径
    let input_path_obj = Path::new(&input_path);
    let parent_dir = input_path_obj.parent().unwrap();
    let file_stem = input_path_obj.file_stem().unwrap().to_str().unwrap();
    let output_path = parent_dir.join(format!("{}.{}", file_stem, format));

    println!("开始视频转动图: {} -> {:?}", input_path, output_path);
    println!(
        "参数: format={}, start_time={}, duration={}, fps={}, quality={}",
        format, start_time, duration, fps, quality
    );

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("VIDEOTOGIF_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    let result = match format.as_str() {
        "gif" => {
            create_gif_with_palette(
                &window,
                &input_path,
                &output_path,
                start_time,
                duration,
                fps,
                width,
                height,
                quality,
                loop_count,
            )
            .await
        }
        "webp" => {
            create_webp_animated(
                &window,
                &input_path,
                &output_path,
                start_time,
                duration,
                fps,
                width,
                height,
                quality,
                loop_count,
            )
            .await
        }
        _ => Err(format!("不支持的格式: {}", format)),
    };

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    // 发送完成事件或错误事件
    match &result {
        Ok(_) => {
            window
                .emit("VIDEOTOGIF_PROGRESS", Payload { pct: 100.0 })
                .unwrap();
        }
        Err(error) => {
            // 发送错误事件，让前端能够检测到错误
            window
                .emit("VIDEOTOGIF_ERROR", serde_json::json!({ "error": error }))
                .unwrap();
        }
    }

    result
}

// 使用调色板创建高质量GIF
async fn create_gif_with_palette(
    window: &tauri::Window,
    input_path: &str,
    output_path: &Path,
    start_time: f32,
    duration: f32,
    fps: f32,
    width: Option<i32>,
    height: Option<i32>,
    quality: u8,
    loop_count: i32,
) -> Result<(), String> {
    // 创建临时调色板文件
    let palette_path = output_path.with_extension("palette.png");

    // 构建视频滤镜
    let mut video_filter = format!("fps={}", fps);

    // 添加缩放滤镜
    if let (Some(w), Some(h)) = (width, height) {
        video_filter = format!("{},scale={}:{}:flags=lanczos", video_filter, w, h);
    } else if let Some(w) = width {
        video_filter = format!("{},scale={}:-1:flags=lanczos", video_filter, w);
    } else if let Some(h) = height {
        video_filter = format!("{},scale=-1:{}:flags=lanczos", video_filter, h);
    }

    // 第一步：生成调色板
    window
        .emit("VIDEOTOGIF_PROGRESS", Payload { pct: 25.0 })
        .unwrap();

    let palette_filter = format!(
        "{},palettegen=max_colors=256:reserve_transparent=0",
        video_filter
    );

    let palette_args = vec![
        "-ss".to_string(),
        start_time.to_string(),
        "-t".to_string(),
        duration.to_string(),
        "-i".to_string(),
        input_path.to_string(),
        "-vf".to_string(),
        palette_filter,
        "-y".to_string(),
        palette_path.to_str().unwrap().to_string(),
    ];

    let mut ffmpeg_palette = config::create_ffmpeg_command()?
        .args(&palette_args)
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg palette spawn error] {e:?}");
            format!("FFmpeg调色板生成启动失败: {e}")
        })?;

    let palette_status = ffmpeg_palette.wait().map_err(|e| {
        println!("[FFmpeg palette wait error] {e:?}");
        format!("FFmpeg调色板生成等待失败: {e}")
    })?;

    if !palette_status.success() {
        return Err("调色板生成失败".to_string());
    }

    // 第二步：使用调色板生成GIF
    window
        .emit("VIDEOTOGIF_PROGRESS", Payload { pct: 50.0 })
        .unwrap();

    // 构建抖动参数
    let dither = match quality {
        1..=3 => "none",
        4..=6 => "bayer:bayer_scale=2",
        7..=8 => "floyd_steinberg",
        _ => "sierra2_4a",
    };

    let gif_filter = format!("{}[x];[x][1:v]paletteuse=dither={}", video_filter, dither);

    let mut gif_args = vec![
        "-ss".to_string(),
        start_time.to_string(),
        "-t".to_string(),
        duration.to_string(),
        "-i".to_string(),
        input_path.to_string(),
        "-i".to_string(),
        palette_path.to_str().unwrap().to_string(),
        "-filter_complex".to_string(),
        gif_filter,
    ];

    // 添加循环参数 (GIF: loop -1 = 播放一次, loop 0 = 无限循环, loop N = 循环N次后停止)
    if loop_count == -1 {
        // 无限循环
        gif_args.extend_from_slice(&["-loop".to_string(), "0".to_string()]);
    } else if loop_count == 1 {
        // 播放一次（不循环）
        gif_args.extend_from_slice(&["-loop".to_string(), "-1".to_string()]);
    } else if loop_count > 1 {
        // 播放N次 = 循环(N-1)次，因为GIF的loop 1表示循环1次（总共播放2次）
        gif_args.extend_from_slice(&["-loop".to_string(), (loop_count - 1).to_string()]);
    }
    // loop_count == 0 时不添加循环参数，使用默认行为

    gif_args.extend_from_slice(&["-y".to_string(), output_path.to_str().unwrap().to_string()]);

    let mut ffmpeg_gif = config::create_ffmpeg_command()?
        .args(&gif_args)
        .print_command()
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg GIF spawn error] {e:?}");
            format!("FFmpeg GIF生成启动失败: {e}")
        })?;

    // 监控进度
    let mut last_pct = 50.0f32;
    for progress in ffmpeg_gif
        .iter()
        .map_err(|e| format!("FFmpeg GIF处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频转动图处理已取消");
            kill_ffmpeg_process();
            // 清理临时文件
            let _ = std::fs::remove_file(&palette_path);
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let estimated_frames = (duration * fps) as f32;
        let current_pct = 50.0 + (frame as f32 / estimated_frames * 50.0).min(50.0);

        if current_pct > last_pct + 1.0 {
            window
                .emit("VIDEOTOGIF_PROGRESS", Payload { pct: current_pct })
                .unwrap();
            last_pct = current_pct;
        }
    }

    // 清理临时调色板文件
    let _ = std::fs::remove_file(&palette_path);

    Ok(())
}

// 创建WebP动画
async fn create_webp_animated(
    window: &tauri::Window,
    input_path: &str,
    output_path: &Path,
    start_time: f32,
    duration: f32,
    fps: f32,
    width: Option<i32>,
    height: Option<i32>,
    quality: u8,
    loop_count: i32,
) -> Result<(), String> {
    // 构建视频滤镜
    let mut video_filter = format!("fps={}", fps);

    // 添加缩放滤镜
    if let (Some(w), Some(h)) = (width, height) {
        video_filter = format!("{},scale={}:{}:flags=lanczos", video_filter, w, h);
    } else if let Some(w) = width {
        video_filter = format!("{},scale={}:-1:flags=lanczos", video_filter, w);
    } else if let Some(h) = height {
        video_filter = format!("{},scale=-1:{}:flags=lanczos", video_filter, h);
    }

    // 构建WebP参数
    let mut webp_args = vec![
        "-ss".to_string(),
        start_time.to_string(),
        "-t".to_string(),
        duration.to_string(),
        "-i".to_string(),
        input_path.to_string(),
        "-vf".to_string(),
        video_filter,
        "-c:v".to_string(),
        "libwebp".to_string(),
        "-quality".to_string(),
        // 直接使用质量值，确保在1-100范围内
        quality.clamp(1, 100).to_string(),
        "-preset".to_string(),
        "default".to_string(),
    ];

    // 添加循环参数 (WebP: loop 0 = 无限循环, loop N = 播放N次)
    if loop_count == -1 {
        // 无限循环
        webp_args.extend_from_slice(&["-loop".to_string(), "0".to_string()]);
    } else if loop_count > 0 {
        // 播放N次
        webp_args.extend_from_slice(&["-loop".to_string(), loop_count.to_string()]);
    }
    // loop_count == 0 时不添加循环参数，播放一次后停止

    webp_args.extend_from_slice(&["-y".to_string(), output_path.to_str().unwrap().to_string()]);

    let mut ffmpeg_webp = config::create_ffmpeg_command()?
        .args(&webp_args)
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg WebP spawn error] {e:?}");
            format!("FFmpeg WebP处理启动失败: {e}")
        })?;

    // 监控进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg_webp
        .iter()
        .map_err(|e| format!("FFmpeg WebP处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频转WebP处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let estimated_frames = (duration * fps) as f32;
        let current_pct = (frame as f32 / estimated_frames * 100.0).min(100.0);

        if current_pct > last_pct + 1.0 {
            window
                .emit("VIDEOTOGIF_PROGRESS", Payload { pct: current_pct })
                .unwrap();
            last_pct = current_pct;
        }
    }

    Ok(())
}

// 生成随机时间戳的辅助函数
fn generate_random_timestamps(count: u32, start_offset: f32, usable_duration: f32) -> Vec<f32> {
    let mut rng = rand::thread_rng();
    let mut timestamps: Vec<f32> = Vec::new();

    // 生成不重复的随机时间点
    while timestamps.len() < count as usize {
        let timestamp = start_offset + rng.gen::<f32>() * usable_duration;
        // 确保时间点之间至少间隔1秒
        if !timestamps.iter().any(|&t| (t - timestamp).abs() < 1.0) {
            timestamps.push(timestamp);
        }
    }

    // 排序时间点
    timestamps.sort_by(|a, b| a.partial_cmp(b).unwrap());
    timestamps
}
