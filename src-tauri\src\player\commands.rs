use crate::player::mpv::{MpvError, MpvPlayer};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::ffi::OsString;
use std::os::windows::ffi::OsStringExt;
use std::sync::{Arc, Mutex};
use tauri::{Emitte<PERSON>, Manager};
use winapi::shared::minwindef::{BOOL, LPARAM};
use winapi::shared::windef::HWND;
use winapi::um::winuser::{EnumWindows, GetClassNameW, GetWindowTextW};

// Global instance of mpv player for preview
static MPV_PREVIEW_PLAYER: Lazy<Mutex<Option<Arc<MpvPlayer>>>> = Lazy::new(|| Mutex::new(None));

#[tauri::command]
pub async fn init_mpv_player() -> Result<(), MpvError> {
    // 尝试多个可能的路径
    let possible_paths = [
        "./lib/mpv/libmpv-2.dll",
        "lib/mpv/libmpv-2.dll",
        "src-tauri/lib/mpv/libmpv-2.dll",
        "./src-tauri/lib/mpv/libmpv-2.dll",
        "libmpv-2.dll",
    ];

    let mut last_error = None;
    for path in &possible_paths {
        match MpvPlayer::new(path) {
            Ok(player) => {
                // 初始化MPV播放器，让它创建独立窗口
                player.initialize()?;

                let mut global_player = MPV_PREVIEW_PLAYER.lock().unwrap();
                *global_player = Some(player);

                return Ok(());
            }
            Err(e) => {
                last_error = Some(e);
                continue;
            }
        }
    }

    Err(last_error.unwrap_or(MpvError::InitializationError))
}

// 新的函数：使用指定窗口句柄初始化MPV
pub async fn init_mpv_with_window(window_handle: usize) -> Result<(), MpvError> {
    // 尝试多个可能的路径
    let possible_paths = [
        "./lib/mpv/libmpv-2.dll",
        "lib/mpv/libmpv-2.dll",
        "src-tauri/lib/mpv/libmpv-2.dll",
        "./src-tauri/lib/mpv/libmpv-2.dll",
        "libmpv-2.dll",
    ];

    let mut last_error = None;
    for path in &possible_paths {
        match MpvPlayer::new(path) {
            Ok(player) => {
                // 将MPV嵌入到指定窗口
                player.attach_to_window(window_handle)?;

                // 初始化MPV播放器
                player.initialize()?;

                let mut global_player = MPV_PREVIEW_PLAYER.lock().unwrap();
                *global_player = Some(player);

                return Ok(());
            }
            Err(e) => {
                last_error = Some(e);
                continue;
            }
        }
    }

    Err(last_error.unwrap_or(MpvError::InitializationError))
}

#[tauri::command]
pub async fn cleanup_mpv_player() -> Result<(), MpvError> {
    let mut global_player = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = global_player.take() {
        // 先尝试停止播放
        let _ = player.stop();

        // 然后发送quit命令来优雅关闭
        let _ = player.quit();

        // 释放锁，让其他操作可以进行
        drop(global_player);

        // 等待一下让MPV处理quit命令
        std::thread::sleep(std::time::Duration::from_millis(300));

        // 如果有窗口句柄，尝试强制关闭窗口
        if let Some(hwnd) = get_mpv_window_handle() {
            unsafe {
                use winapi::um::winuser::{DestroyWindow, IsWindow, PostMessageW, WM_CLOSE};

                // 检查窗口是否还存在
                if IsWindow(hwnd) != 0 {
                    // 先尝试发送关闭消息
                    PostMessageW(hwnd, WM_CLOSE, 0, 0);
                    std::thread::sleep(std::time::Duration::from_millis(200));

                    // 如果窗口还存在，强制销毁
                    if IsWindow(hwnd) != 0 {
                        DestroyWindow(hwnd);
                    }
                }
            }
        }

        // 最后销毁播放器
        player.destroy()?;
    }
    Ok(())
}

// 同步版本的清理函数，用于窗口关闭事件
pub fn cleanup_mpv_player_sync() -> Result<(), MpvError> {
    let mut global_player = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = global_player.take() {
        // 先尝试停止播放
        let _ = player.stop();

        // 销毁MPV实例
        player.destroy()?;
        println!("MPV播放器已清理");
    }
    Ok(())
}

// 查找MPV窗口的回调函数
unsafe extern "system" fn enum_windows_proc(hwnd: HWND, _lparam: LPARAM) -> BOOL {
    let mut window_text = [0u16; 256];
    let mut class_name = [0u16; 256];

    GetWindowTextW(hwnd, window_text.as_mut_ptr(), 256);
    GetClassNameW(hwnd, class_name.as_mut_ptr(), 256);

    let title =
        OsString::from_wide(&window_text[..window_text.iter().position(|&x| x == 0).unwrap_or(0)]);
    let class =
        OsString::from_wide(&class_name[..class_name.iter().position(|&x| x == 0).unwrap_or(0)]);

    // 检查是否是MPV窗口（通过窗口类名或标题）
    if class.to_string_lossy().contains("mpv") || title.to_string_lossy().contains("mpv") {
        // 找到MPV窗口，保存句柄
        let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
        if let Some(player) = player_guard.as_ref() {
            player.set_window_handle(hwnd as usize);
        }
        return 0; // 停止枚举
    }

    1 // 继续枚举
}

// 查找并保存MPV窗口句柄
fn find_mpv_window() {
    unsafe {
        EnumWindows(Some(enum_windows_proc), 0);
    }
}

// 获取MPV窗口句柄的函数
pub fn get_mpv_window_handle() -> Option<HWND> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        if let Some(handle_usize) = player.get_window_handle() {
            Some(handle_usize as HWND)
        } else {
            // 如果没有保存的句柄，尝试查找MPV窗口
            drop(player_guard); // 释放锁
            find_mpv_window();

            // 重新获取锁并检查
            let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
            if let Some(player) = player_guard.as_ref() {
                if let Some(handle_usize) = player.get_window_handle() {
                    Some(handle_usize as HWND)
                } else {
                    None
                }
            } else {
                None
            }
        }
    } else {
        None
    }
}

#[tauri::command]
pub fn preview_mpv_load_file(path: String) -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        let result = player.load_file(&path);

        // 加载文件后，尝试查找MPV窗口
        drop(player_guard); // 释放锁
        std::thread::sleep(std::time::Duration::from_millis(500)); // 等待窗口创建
        find_mpv_window();

        result
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_play() -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.play()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_pause() -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.pause()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_stop() -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.stop()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_seek(position: f64) -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.seek(position)
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_seek_relative(offset: f64) -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.seek_relative(offset)
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_get_position() -> Result<f64, MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.get_position()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_get_duration() -> Result<f64, MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.get_duration()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_is_paused() -> Result<bool, MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.is_paused()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_set_speed(speed: f64) -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.set_speed(speed)
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_get_speed() -> Result<f64, MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.get_speed()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_frame_step() -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.frame_step()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_frame_back_step() -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.frame_back_step()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_set_volume(volume: f64) -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.set_volume(volume)
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_get_volume() -> Result<f64, MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.get_volume()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_set_mute(muted: bool) -> Result<(), MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.set_mute(muted)
    } else {
        Err(MpvError::InitializationError)
    }
}

#[tauri::command]
pub fn preview_mpv_get_mute() -> Result<bool, MpvError> {
    let player_guard = MPV_PREVIEW_PLAYER.lock().unwrap();
    if let Some(player) = player_guard.as_ref() {
        player.get_mute()
    } else {
        Err(MpvError::InitializationError)
    }
}

#[derive(Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SegmentData {
    pub start_time: f64,
    pub end_time: f64,
    pub start_time_str: String,
    pub end_time_str: String,
}

#[derive(Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SegmentsPayload {
    pub segments: Vec<SegmentData>,
    pub video_path: String,
}

#[tauri::command]
pub async fn emit_segments_to_main(
    app_handle: tauri::AppHandle,
    segments: Vec<SegmentData>,
    video_path: String,
) -> Result<(), String> {
    // 发送事件到主窗口
    if let Some(main_window) = app_handle.get_webview_window("main") {
        let payload = SegmentsPayload {
            segments,
            video_path,
        };

        main_window
            .emit("segments_exported", payload)
            .map_err(|e| format!("发送事件失败: {}", e))?;

        Ok(())
    } else {
        Err("找不到主窗口".to_string())
    }
}
