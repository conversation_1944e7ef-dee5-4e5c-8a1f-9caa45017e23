[package]
name = "VideoIDE"
version = "0.1.0"
description = "A video processing App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "videoide_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tauri-plugin-dialog = "2"
tauri-plugin-drag = "2.1.0"
ffmpeg-sidecar = "2.0.6"
anyhow = "1.0.98"
tokio = { version = "1", features = ["process"] }
rand = "0.8"
libloading = "0.7"
winapi = { version = "0.3", features = ["winuser", "libloaderapi"] }
once_cell = "1.7"
thiserror = "1.0"
urlencoding = "2.1"
chrono = { version = "0.4", features = ["serde"] }

